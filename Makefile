# Setting SHEL<PERSON> to bash allows bash commands to be executed by recipes.
# Options are set to exit when a recipe line exits non-zero or a piped command fails.
SHELL=/usr/bin/env bash -o pipefail

.DEFAULT_GOAL:=help

.PHONY: help
help:  # Display this help
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n\nTargets:\n"} /^[0-9A-Za-z_-]+:.*?##/ { printf "  \033[36m%-45s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)
## --------------------------------------
## define some global variables used in Makefile
## --------------------------------------
#
REPO                       := lcm-manager
TAG                        ?= $(shell git describe --tags --always --dirty)
RELEASE_TIME               ?= $(shell date -u +'%Y-%m-%dT%H:%M:%SZ')
DATE_STRING                := $(shell date +%Y%m%d%H%M | cut -c3-)
RELEASE_VERSION            := $(TAG)
DEV_VERSION                ?= $(shell git describe --tags | cut -d- -f1)-dev-$(DATE_STRING)
DEV_ENV                    := false
PRODUCT_VENDOR             ?= smtx
PRODUCT_VENDORS            := smtx arcfra

ifeq ($(DEV_ENV), true)
	RELEASE_VERSION = $(DEV_VERSION)
endif

## --------------------------------------
## define golang build variables
## --------------------------------------
#
GO_VERSION ?= 1.22
GOPATH ?= $(shell go env GOPATH)
GO_CONTAINER_IMAGE ?= registry.smtx.io/lcm/lcm-manager-golang:$(GO_VERSION)-bookworm
GO_CONTAINER_IMAGE_ORIGIN := docker.io/library/golang:$(GO_VERSION)-bookworm

GOPRIVATE := "github.smartx.com,192.168.50.150,newgh.smartx.com"

# Use GOPROXY environment variable if set
GOPROXY := $(shell go env GOPROXY)
ifeq ($(GOPROXY),)
GOPROXY := https://goproxy.cn
endif
export GOPROXY

# Active module mode, as we use go modules to manage dependencies
export GO111MODULE=on

# This option is for running docker manifest command
export DOCKER_CLI_EXPERIMENTAL := enabled

#
# Directories.
#
# Full directory of where the Makefile resides
ROOT_DIR  := $(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))
BIN_DIR   := $(ROOT_DIR)/bin
TEST_DIR  := $(ROOT_DIR)/test

# Set build time variables including version details
LDFLAGS := $(shell build/scripts/version.sh)

## --------------------------------------
##@ golang binary tools:
## Note: Need to use abspath so we can invoke these from subdirectories
## --------------------------------------
#
# gofumpt is a stricter gofmt
GOFUMPT                := $(GOPATH)/bin/gofumpt
# golang source code linter
GOLANGCI_LINT          := $(GOPATH)/bin/golangci-lint
# regctl is a command line tool for interacting with container registries
REGCTL                 := $(GOPATH)/bin/regctl

# pure Go implementation of jq
GOJQ                   := $(GOPATH)/bin/gojq

# yq tool for update helm chart values.yaml
GOYQ                    := $(GOPATH)/bin/yq

# define golang binary tools pkg path and version
GOFUMPT_PKG            := mvdan.cc/gofumpt
GOLANGCI_LINT_PKG      := github.com/golangci/golangci-lint/cmd/golangci-lint
REGCTL_PKG             := github.com/regclient/regclient/cmd/regctl
GOJQ_PKG               := github.com/itchyny/gojq/cmd/gojq
GOYQ_PKG               := github.com/mikefarah/yq/v4
REGCTL_TAG             ?= v0.5.7
GOFUMPT_VERSION        ?= v0.5.0
GOLANGCI_LINT_VERSION  ?= v1.59.1
GOFUMPT_VERSION        ?= v0.5.0
GOJQ_VERSION           ?= v0.12.15
GOYQ_VERSION           ?= v4.44.3
TYPOS                  ?= typos
GOLANG_TOOLS           := $(GOFUMPT) $(GOLANGCI_LINT) $(REGCTL) $(GOJQ) $(GOYQ) grpc
.PHONY: dev-tools $(GOLANG_TOOLS)
dev-tools: $(GOLANG_TOOLS) ## install golang binary tools for dev env

# define dynamic target for auto install golang binary tools if not exist
$(GOFUMPT): ## check gofumpt binary tool exist or not, if not exist, install it
	$(GOFUMPT) --version >/dev/null 2>&1 || go install $(GOFUMPT_PKG)@$(GOFUMPT_VERSION)

$(GOLANGCI_LINT): ## check golangci-lint binary tool exist or not, if not exist, install it
	$(GOLANGCI_LINT) version >/dev/null 2>&1 || go install $(GOLANGCI_LINT_PKG)@$(GOLANGCI_LINT_VERSION)

$(REGCTL): ## check regctl binary tool exist or not, if not exist, install it
	$(REGCTL) version >/dev/null 2>&1 || go install $(REGCTL_PKG)@$(REGCTL_TAG)

$(GOJQ):
	$(GOJQ) --version >/dev/null 2>&1 || go install $(GOJQ_PKG)@$(GOJQ_VERSION)

$(GOYQ):
	$(GOYQ) --version >/dev/null 2>&1 || go install $(GOYQ_PKG)@$(GOYQ_VERSION)

grpc:
	go install github.com/bufbuild/buf/cmd/buf@v1.35.1
	go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.34.2
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.5.1
	go install github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-grpc-gateway@v2.21.0
	go install github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2@v2.21.0


## --------------------------------------
##@ docker build container image:
## --------------------------------------
#
# Define Docker related variables. Releases should modify and double check these vars.
REGISTRY                       ?= registry.smtx.io
ALL_PLATFORMS                  := linux/amd64,linux/arm64
PLATFORMS                      ?= $(ALL_PLATFORMS)

# define lcm-manager server, worker, agent container base image
BASE_IMAGE                     := docker.io/bitnami/minideb:bookworm
APP_BASE_IMAGE_DOCKERFILE      := build/app-base-image/Dockerfile
APP_BASE_IMAGE_TAG             ?= debian12-$(shell cat $(APP_BASE_IMAGE_DOCKERFILE) | sha256sum | cut -c 1-12)
APP_BASE_IMAGE                 := $(REGISTRY)/lcm/lcm-manager-base:$(APP_BASE_IMAGE_TAG)

LCM_MANAGER_IMAGE_NAME      := lcm-manager
LCM_MANAGER_IMAGE_TAG       ?= $(RELEASE_VERSION)
LCM_MANAGER_IMAGE           := $(REGISTRY)/lcm/$(LCM_MANAGER_IMAGE_NAME):$(LCM_MANAGER_IMAGE_TAG)
FORCE_BUILD                    ?= false
BUILD_PROGRESS                 ?= plain
NO_CACHE                       := $(shell if [ "$(FORCE_BUILD)" = "true" ]; then echo -n "--no-cache"; fi)
HOST_PLUGIN_BUILDER_IMAGE      ?= registry.smtx.io/host-plugin/builder:0.2.2
LCM_MANAGER_AGENT_IMAGE_NAME       := lcm-manager-agent
LCM_MANAGER_AGENT_IMAGE_TAG        ?= $(RELEASE_VERSION)
LCM_MANAGER_AGENT_IMAGE            := $(REGISTRY)/lcm/$(LCM_MANAGER_AGENT_IMAGE_NAME):$(LCM_MANAGER_AGENT_IMAGE_TAG)
HOST_PLUGIN_PKGS_IMAGE_NAME    := lcm-host-plugin-package
HOST_PLUGIN_PKGS_IMAGE_TAG     ?= $(RELEASE_VERSION)
HOST_PLUGIN_PKGS_IMAGE         := $(REGISTRY)/lcm/$(HOST_PLUGIN_PKGS_IMAGE_NAME):$(HOST_PLUGIN_PKGS_IMAGE_TAG)

DOCKER_BUILD_PROD_TARGETS      := build-host-plugin-pkgs-image build-server-image
DOCKER_BUILD_TARGETS           := vendor $(DOCKER_BUILD_PROD_TARGETS)
ifneq ($(DEV_ENV), false)
	DOCKER_BUILD_TARGETS = gen-docs vendor $(DOCKER_BUILD_PROD_TARGETS)
endif

DOCKER_BUILD_ARGS := --push \
    $(NO_CACHE) \
	--provenance=false \
	--progress=$(BUILD_PROGRESS) \
	--label ldflags="$(LDFLAGS)" \
	--build-arg goproxy=$(GOPROXY) \
	--build-arg ldflags="$(LDFLAGS)" \
	--build-arg goprivate=$(GOPRIVATE) \
	--build-arg golang_builder_image=$(GO_CONTAINER_IMAGE)

.PHONY: build-app-base-image docker-build build-server-image build-agent-image build-host-plugin-pkgs-image upload-host-plugin-pkgs sync-golang-image
docker-build: $(DOCKER_BUILD_TARGETS) ## build lcm-manager server and agent container image

sync-golang-image:
	@mkdir -p $(PWD)/temp/empty
	echo FROM $(GO_CONTAINER_IMAGE_ORIGIN) > $(PWD)/temp/empty/Dockerfile
	docker buildx build \
	--push \
	--platform=$(ALL_PLATFORMS) \
	-t $(GO_CONTAINER_IMAGE) \
	$(PWD)/temp/empty
	rm -rf $(PWD)/temp/empty

build-app-base-image:
	docker buildx build \
	$(DOCKER_BUILD_ARGS) \
	--platform=$(ALL_PLATFORMS) \
	--build-arg base_image=$(BASE_IMAGE) \
	--label base_image=$(BASE_IMAGE) \
	-f $(APP_BASE_IMAGE_DOCKERFILE) \
	-t $(APP_BASE_IMAGE) \
	.
	@echo -e "app base image: \\033[32;1m $(APP_BASE_IMAGE) \\033[0;39m"

build-server-image: ## build lcm-manager server container image
	docker buildx build \
	$(DOCKER_BUILD_ARGS) \
	--platform=$(PLATFORMS) \
	--build-arg base_image=$(APP_BASE_IMAGE) \
	--build-arg host_plugin_pkgs_image=$(HOST_PLUGIN_PKGS_IMAGE) \
	-f build/lcm-manager/Dockerfile \
	-t $(LCM_MANAGER_IMAGE) \
	.

build-agent-image: ## build lcm-manager agent container image
	docker buildx build \
	$(DOCKER_BUILD_ARGS) \
	--platform=$(PLATFORMS) \
	--build-arg base_image=$(APP_BASE_IMAGE) \
	-f build/lcm-manager-agent/Dockerfile \
	-t $(LCM_MANAGER_AGENT_IMAGE) \
	.

build-host-plugin-pkgs-image: build-agent-image ## build host plugin package artifact container image
	docker buildx build \
	$(DOCKER_BUILD_ARGS) \
	--platform=linux/amd64 \
	--build-arg hpp_builder_image=$(HOST_PLUGIN_BUILDER_IMAGE) \
	--build-arg lcm_manager_agent_image=$(LCM_MANAGER_AGENT_IMAGE) \
	--build-arg lcm_manager_agent_version=$(LCM_MANAGER_AGENT_IMAGE_TAG) \
	-f build/host-plugin-package/Dockerfile \
	-t $(HOST_PLUGIN_PKGS_IMAGE) \
	build/host-plugin-package

# build-host-plugin-pkgs: build-host-plugin-pkg-image download-host-plugin-pkg-bundle ## build and download host plugin package artiface to output dir
download-host-plugin-pkgs: ## download host plugin package artifact to output dir
	@mkdir -p $(PWD)/temp/empty
	echo FROM $(HOST_PLUGIN_PKGS_IMAGE) | docker buildx build --output type=local,dest=$(PWD)/output -f - $(PWD)/temp/empty
	@find $(PWD)/output -type f
	@rm -rf $(PWD)/temp/empty

.PHONY: upload-host-plugin-pkgs
upload-host-plugin-pkgs: ## upload host plugin packages to cloudtower using upload_host-plugin_pkg.sh
	bash $(ROOT_DIR)/hack/upload_host-plugin_pkg.sh $(PWD)/output

.PHONY: cleanup-images
cleanup-images:
	$(REGCTL) image delete --force-tag-dereference $(LCM_MANAGER_IMAGE) 2>/dev/null || true
	$(REGCTL) image delete --force-tag-dereference $(HOST_PLUGIN_PKGS_IMAGE) 2>/dev/null || true
	$(REGCTL) image delete --force-tag-dereference $(LCM_MANAGER_AGENT_IMAGE) 2>/dev/null || true

## --------------------------------------
##@ deploy lcm-manager server:
## --------------------------------------
#
CHART_VERSION := $(RELEASE_VERSION)
CHART_SUFFIX  := $(shell echo $(PRODUCT_VENDOR) | tr '[:upper:]' '[:lower:]' | sed 's/smtx/smartx/')
CHART_REPO    := lcm/charts-$(CHART_SUFFIX)
RELEASE_NAME  := lcm-manager
TMP_DIR       := /tmp/$(RELEASE_NAME)-charts

OEM_CHART_IMAGE_REGISTRY := registry.local
CHART_IMAGE_REGISTRY ?= $(REGISTRY)

ifneq ($(PRODUCT_VENDOR), smtx)
	CHART_IMAGE_REGISTRY = $(OEM_CHART_IMAGE_REGISTRY)
endif

PUSH_CHART_TARGETS    := $(addprefix push-chart-,$(PRODUCT_VENDORS))

.PHONY: deploy push-charts deploy-chart
deploy: docker-build deploy-chart ## build and deploy lcm-manager server to k8s cluster

build-and-push-charts: docker-build push-charts ## build and push lcm-manager server to oci registry

push-chart: ## package and push helm chart to oci registry
	@mkdir -p $(TMP_DIR)
	@rm -rf $(TMP_DIR)/$(RELEASE_NAME)
	@cp -rf deploy/$(RELEASE_NAME) $(TMP_DIR)/$(RELEASE_NAME)
	$(GOYQ) e -i '.platformConfig.productVendor = "$(PRODUCT_VENDOR)"' $(TMP_DIR)/$(RELEASE_NAME)/values.yaml
	$(GOYQ) e -i '.platformConfig.imageRegistry = "$(CHART_IMAGE_REGISTRY)"' $(TMP_DIR)/$(RELEASE_NAME)/values.yaml
	helm package --app-version=$(RELEASE_VERSION) --version=$(CHART_VERSION) $(TMP_DIR)/$(RELEASE_NAME) -d $(TMP_DIR)
	HELM_EXPERIMENTAL_OCI=1 helm push $(TMP_DIR)/$(RELEASE_NAME)-$(CHART_VERSION).tgz oci://$(REGISTRY)/$(CHART_REPO)
	@rm -rf $(TMP_DIR)/$(RELEASE_NAME) $(TMP_DIR)/$(RELEASE_NAME)-$(CHART_VERSION).tgz

push-charts: $(PUSH_CHART_TARGETS)
$(PUSH_CHART_TARGETS):
	$(MAKE) push-chart PRODUCT_VENDOR=$(subst push-chart-,,$@)

deploy-chart: ## package and deploy helm chart to k8s cluster
	@mkdir -p $(TMP_DIR)
	@rm -rf $(TMP_DIR)/$(RELEASE_NAME)
	@cp -rf deploy/$(RELEASE_NAME) $(TMP_DIR)/$(RELEASE_NAME)
	helm package --app-version=$(RELEASE_VERSION) --version=$(CHART_VERSION) $(TMP_DIR)/$(RELEASE_NAME) -d $(TMP_DIR)
	helm upgrade $(HELM_ARGS) \
		--wait \
		--atomic \
		--timeout 5m \
		--set platformConfig.productVendor=$(PRODUCT_VENDOR) \
		--set platformConfig.imageRegistry=$(REGISTRY) \
		--set ingress.enabled=true \
		--set image.pullPolicy=Always \
		--set swagger.enabled=true \
		--set pprof.enabled=true \
		--namespace=cloudtower-system \
		-i $(RELEASE_NAME) $(TMP_DIR)/$(RELEASE_NAME)-$(CHART_VERSION).tgz
	@rm -rf $(TMP_DIR)/$(RELEASE_NAME) $(TMP_DIR)/$(RELEASE_NAME)-$(CHART_VERSION).tgz

## --------------------------------------
##@ lint and verify:
## --------------------------------------
#
.PHONY: vendor
vendor: ## run go mod tidy and go mod vendor to ensure modules are up to date
	GOPRIVATE=$(GOPRIVATE) GOINSECURE=$(GOPRIVATE) GONOSUMDB=$(GOPRIVATE) go mod tidy
	GOPRIVATE=$(GOPRIVATE) GOINSECURE=$(GOPRIVATE) GONOSUMDB=$(GOPRIVATE) go mod vendor

.PHONY: lint
lint: ## lint the codebase
	$(GOLANGCI_LINT) run -v $(GOLANGCI_LINT_EXTRA_ARGS)

.PHONY: lint-fix
lint-fix: ## lint the codebase and run auto-fixers if supported by the linter
	GOLANGCI_LINT_EXTRA_ARGS=--fix $(MAKE) lint

.PHONY: lint-cache-clean
lint-cache-clean: ## avoid the issue https://github.com/golangci/golangci-lint/issues/1940
	$(GOLANGCI_LINT) cache clean

.PHONY: format
format: ## format the codebase by gofumpt
	$(GOFUMPT) -l -w -e -d .

.PHONY: typos
typos:
	$(TYPOS) --sort

## --------------------------------------
##@ test and coverage:
## --------------------------------------

.PHONY: test
test: ## Run unit and integration tests
	mkdir -p out
	go test -v ./... $(TEST_ARGS)

.PHONY: test-junit
test-junit: $(SETUP_ENVTEST) $(GOTESTSUM) ## Run tests with verbose setting and generate a junit report
	set +o errexit; (KUBEBUILDER_ASSETS="$(KUBEBUILDER_ASSETS)" go test -json ./... $(TEST_ARGS); echo $$? > $(ARTIFACTS)/junit.exitcode) | tee $(ARTIFACTS)/junit.stdout
	$(GOTESTSUM) --junitfile $(ARTIFACTS)/junit.xml --raw-command cat $(ARTIFACTS)/junit.stdout
	exit $$(cat $(ARTIFACTS)/junit.exitcode)

.PHONY: test-cover
test-cover: ## Run tests with code coverage and code generate reports
	$(MAKE) test TEST_ARGS="$(TEST_ARGS) -coverprofile=out/coverage.out"
	go tool cover -func=out/coverage.out -o out/coverage.txt
	go tool cover -html=out/coverage.out -o out/coverage.html

.PHONY: test-verbose
test-verbose: ## Run tests with verbose settings
	$(MAKE) test TEST_ARGS="$(TEST_ARGS) -v"


## --------------------------------------
##@ tower sdk code generate:
## --------------------------------------
#
GENQLIENT_PKG             := github.com/Khan/genqlient
GENQLIENT_PKG_VERSION     := $(shell grep '$(GENQLIENT_PKG)' go.mod | cut -d' ' -f2 | head -n1)
TOWER_GIT_TAG             ?= v4.1.0-rc-2024-03-12-0
TOWER_GIT_URL             := *********************:frontend/tower.git
TOWER_SCHEMA_FILE         := $(ROOT_DIR)/third_party/tower/schema.graphql

download-tower-schema: ## download tower graphql schema file from gitlab
	git archive --remote=$(TOWER_GIT_URL) $(TOWER_GIT_TAG):packages/graphql-schema -- schema.graphql | tar -O -xf - > $(TOWER_SCHEMA_FILE)

generate-tower-sdk: ## generate cloudtower sdk code by genqlient using tower graphql schema file
	cd third_party/tower && go run $(GENQLIENT_PKG)@$(GENQLIENT_PKG_VERSION)

## --------------------------------------
##@ build jenkins-ci pod image:
## --------------------------------------
JENKINS_CI_POD_DIR            ?= $(ROOT_DIR)/build/jenkins-ci
JENKINS_CI_POD_IMAGE_ID       ?= $(shell git log -1 --pretty=oneline --pretty=format:"%ad" --date=short $(JENKINS_CI_POD_DIR) Makefile)
JENKINS_CI_POD_IMAGE_TAG      ?= lcm-manager-$(JENKINS_CI_POD_IMAGE_ID)
JENKINS_CI_POD_IMAGE          ?= registry.smtx.io/lcm/jenkins-ci-pod:$(JENKINS_CI_POD_IMAGE_TAG)

.PHONY: build-jenkins-ci-pod-image
build-jenkins-ci-pod-image: ## build jenkins-ci pod image
	docker buildx build \
	$(DOCKER_BUILD_ARGS) \
	--platform=$(ALL_PLATFORMS) \
	-t $(JENKINS_CI_POD_IMAGE) \
	-f $(JENKINS_CI_POD_DIR)/Dockerfile \
	.
	@echo -e "Jenkins CI POD_IMAGE: \\033[32;1m $(JENKINS_CI_POD_IMAGE) \\033[0;39m"
	@echo -e "\\033[33;1m Don't forget to update the image tag in the Jenkinsfile 'POD_IMAGE' params\\033[0;39m"

## --------------------------------------
##@ openapi docs generate:
## --------------------------------------
#
DOCS_DIR               := gen
SWAGGER_JSON           := $(DOCS_DIR)/apidocs.swagger.json
OPENAPI_JSON           := $(DOCS_DIR)/openapi.json

gen-docs: buf-format buf-build convert-openapi ## generate swagger and opeapi docs for server

buf-format:
	buf format -w

buf-build:
	buf generate -v

convert-openapi: ## convert swagger 2.0 spec to openapi 3.0
	bash $(ROOT_DIR)/hack/swagger_converter.sh $(SWAGGER_JSON) $(OPENAPI_JSON)

## --------------------------------------
##@ utils:
## --------------------------------------
#

.PHONY: clean run-server run-worker run-init update-meta-cm prepare-metadata
clean: ## clean unused files
	git clean -xdf -e temp/

run-server: ## run lcm-manager server in local env
	source hack/source_local_env.sh && go run main.go server
run-worker:
	source hack/source_local_env.sh && go run main.go worker
run-init:
	source hack/source_local_env.sh && go run main.go init
