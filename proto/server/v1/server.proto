syntax = "proto3";
package lcm.manager.protobuf;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

service Manager {
  // 角色转换前置检查
  rpc ConvertRoleCheck(ConvertRoleCheckRequest) returns (ConvertRoleCheckResponse) {
    option (google.api.http) = {
      post: "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/role_convert_check"
      body: "*"
    };
  }

  // 获取角色转换前置检查结果
  rpc ConvertRoleCheckResult(CheckResultRequest) returns (CheckResultResponse) {
    option (google.api.http) = {get: "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/role_convert_check_result"};
  }

  // 执行角色转换
  rpc ConvertRole(ConvertRoleRequest) returns (ConvertRoleResponse) {
    option (google.api.http) = {
      post: "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/role_convert"
      body: "*"
    };
  }

  // 主机移除前置检查
  rpc RemoveHostCheck(RemoveHostCheckRequest) returns (RemoveHostCheckResponse) {
    option (google.api.http) = {
      post: "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/remove_check"
      body: "*"
    };
  }

  // 获取主机移除前置检查结果
  rpc RemoveHostCheckResult(CheckResultRequest) returns (CheckResultResponse) {
    option (google.api.http) = {get: "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/remove_check_result"};
  }

  // 执行主机移除
  rpc RemoveHost(RemoveHostRequest) returns (RemoveHostResponse) {
    option (google.api.http) = {
      post: "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/remove"
      body: "*"
    };
  }

  // 同步集群时间
  rpc TimeSync(TimeSyncRequest) returns (TimeSyncResponse) {
    option (google.api.http) = {
      post: "/api/v1/clusters/{cluster_uuid}/time/sync"
      body: "*"
    };
  }

  // 开启或关闭 RDMA
  rpc SetRDMA(SetRDMARequest) returns (SetRDMAResponse) {
    option (google.api.http) = {
      post: "/api/v1/clusters/{cluster_uuid}/rdma/toggle"
      body: "*"
    };
  }

  // 列出所有 Jobs
  rpc ListJobs(ListJobsRequest) returns (ListJobsResponse) {
    option (google.api.http) = {get: "/api/v1/jobs"};
  }

  // 获取 Job 详情
  rpc GetJob(GetJobRequest) returns (Job) {
    option (google.api.http) = {get: "/api/v1/jobs/{id=*}"};
  }

  // 获取 Job 日志
  rpc GetJobLogs(GetJobLogsRequest) returns (JobLogs) {
    option (google.api.http) = {get: "/api/v1/jobs/{id=*}/logs"};
  }

  // 获取支持集群变更白名单
  rpc GetSupportedActions(google.protobuf.Empty) returns (AvailabilityMap) {
    option (google.api.http) = {get: "/api/v1/supported_actions"};
  }

  // 健康检查
  rpc Healthz(google.protobuf.Empty) returns (HealthzResponse) {
    option (google.api.http) = {get: "/api/v1/healthz"};
  }
}

enum HostRole {
  ROLE_UNSPECIFIED = 0;
  ROLE_MASTER = 1;
  ROLE_STORAGE = 2;
}

/* ConvertRoleCheckRequest */
message ConvertRoleCheckRequest {
  // Target Host cluster uuid
  string cluster_uuid = 1 [json_name = "cluster_uuid"];
  // Target host uuid
  string host_uuid = 2 [json_name = "host_uuid"];
  // client id for idempotent request
  string client_id = 3 [json_name = "client_id"];
  // current host role
  HostRole current_role = 4 [json_name = "current_role"];
  // target host role
  HostRole target_role = 5 [json_name = "target_role"];
}

message ConvertRoleCheckResponse {
  string client_id = 1 [json_name = "client_id"];
  string job_id = 2 [json_name = "job_id"];
}

message CheckResultRequest {
  string cluster_uuid = 1 [json_name = "cluster_uuid"];
  string host_uuid = 2 [json_name = "host_uuid"];
  string client_id = 3 [json_name = "client_id"];
  string job_id = 4 [json_name = "job_id"];
}

message CheckResultResponse {
  string client_id = 1 [json_name = "client_id"];
  string job_id = 2 [json_name = "job_id"];
  Job job = 3;
  repeated CheckResult check_results = 4 [json_name = "check_results"];
}

message ConvertRoleRequest {
  string cluster_uuid = 1 [json_name = "cluster_uuid"];
  string host_uuid = 2 [json_name = "host_uuid"];
  string client_id = 3 [json_name = "client_id"];
  HostRole current_role = 4 [json_name = "current_role"];
  HostRole target_role = 5 [json_name = "target_role"];
  bool skip_precheck = 6 [json_name = "skip_precheck"];
}

message ConvertRoleResponse {
  string client_id = 1 [json_name = "client_id"];
  string job_id = 2 [json_name = "job_id"];
}

message RemoveHostCheckRequest {
  string cluster_uuid = 1 [json_name = "cluster_uuid"];
  string host_uuid = 2 [json_name = "host_uuid"];
  string client_id = 3 [json_name = "client_id"];
}

message RemoveHostCheckResponse {
  string client_id = 1 [json_name = "client_id"];
  string job_id = 2 [json_name = "job_id"];
}

message RemoveHostRequest {
  string cluster_uuid = 1 [json_name = "cluster_uuid"];
  string host_uuid = 2 [json_name = "host_uuid"];
  string client_id = 3 [json_name = "client_id"];
  bool skip_precheck = 4 [json_name = "skip_precheck"];
}

message RemoveHostResponse {
  string client_id = 1 [json_name = "client_id"];
  string job_id = 2 [json_name = "job_id"];
}

message TimeSyncRequest {
  string cluster_uuid = 1 [json_name = "cluster_uuid"];
  string client_id = 2 [json_name = "client_id"];
  string time = 3 [json_name = "time"];
}

message TimeSyncResponse {
  string client_id = 1 [json_name = "client_id"];
  string job_id = 2 [json_name = "job_id"];
}

message SetRDMARequest {
  string cluster_uuid = 1 [json_name = "cluster_uuid"];
  string client_id = 2 [json_name = "client_id"];
  RDMAState state = 3 [json_name = "state"];
}

message SetRDMAResponse {
  string client_id = 1 [json_name = "client_id"];
  string job_id = 2 [json_name = "job_id"];
}

enum RDMAState {
  RDMA_UNKNOWN = 0;
  RDMA_ON = 1;
  RDMA_OFF = 2;
}

enum JobState {
  JOB_STATE_UNSPECIFIED = 0;
  JOB_STATE_PENDING = 1;
  JOB_STATE_RUNNING = 2;
  JOB_STATE_SUCCESS = 3;
  JOB_STATE_FAILED = 4;
}

message ProgressDetail {
  string name = 1;
  map<string, string> items = 2;
}

message JobProgress {
  string progress = 1;
  string total_time = 2 [json_name = "total_time"];
  ProgressDetail details = 3;
}

enum ErrorCode {
  EC_UNSPECIFIED = 0;
  SECOND_PRE_CHECK_FAILED = 1;
}

message Job {
  string id = 1;
  string cluster_uuid = 2 [json_name = "cluster_uuid"];
  string host_uuid = 3 [json_name = "host_uuid"];
  string name = 4;
  JobState state = 5;
  map<string, string> details = 6;
  JobProgress progress = 7;
  repeated MessageItem messages = 8;
  ErrorCode ec = 9;

  google.protobuf.Timestamp create_time = 20 [json_name = "create_time"];
  google.protobuf.Timestamp update_time = 21 [json_name = "update_time"];
}

message ListJobsRequest {
  string filter = 3;
  string order_by = 4 [json_name = "order_by"];
  int32 page_size = 1 [json_name = "page_size"];
  string page_token = 2 [json_name = "page_token"];
}

message ListJobsResponse {
  repeated Job jobs = 1;
  string next_page_token = 2 [json_name = "next_page_token"];
  string total = 3;
}

message GetJobRequest {
  string id = 1;
}

message GetJobLogsRequest {
  string id = 1;
  int32 offset = 2;
  int32 length = 3;
}

message JobLogs {
  repeated string logs = 1;
  int32 offset = 2;
  int32 next_offset = 3 [json_name = "next_offset"];
}

message HealthzResponse {
  string result = 1;
}

enum CheckState {
  CHECK_STATE_UNSPECIFIED = 0;
  CHECK_STATE_PENDING = 1;
  CHECK_STATE_RUNNING = 2;
  CHECK_STATE_SUCCESS = 3;
  CHECK_STATE_FAILED = 4;
  CHECK_STATE_WARNING = 5;
}

enum MessageLang {
  LANG_UNSPECIFIED = 0;
  LANG_EN = 1;
  LANG_ZH = 2;
}

message MessageItem {
  MessageLang lang = 1;
  string message = 2;
}

message CheckResult {
  string id = 1;
  string job_id = 2 [json_name = "job_id"];
  string check_name = 3 [json_name = "check_name"];
  repeated MessageItem description = 4;

  CheckState state = 5;
  repeated MessageItem messages = 7;

  google.protobuf.Timestamp create_time = 20 [json_name = "create_time"];
  google.protobuf.Timestamp update_time = 21 [json_name = "update_time"];
}

enum ProductName {
  PRODUCT_UNSPECIFIED = 0;
  SMTXOS = 1;
  SMTXZBS = 2;
  SMTXELF = 3;
}

enum ActionType {
  ACTION_TYPE_UNSPECIFIED = 0;
  CONVERT_ROLE = 1;
  REMOVE_HOST = 2;
  TIME_SYNC = 3;
}

message AvailabilityMap {
  string version = 1;
  repeated ProductAction product_actions = 2 [json_name = "product_actions"];
}

message ProductAction {
  ProductName product_name = 1 [json_name = "product_name"];
  repeated Action actions = 2 [json_name = "actions"];
}

message Action {
  string name = 1;
  ActionType type = 2;
  repeated VersionedAction versioned_actions = 3 [json_name = "versioned_actions"];
}

enum ActionFeature {
  ACTION_FEATURE_UNSPECIFIED = 0;
  SUPPORT_MULTI_OFFLINE_HOSTS = 1;
}

message VersionedAction {
  repeated string versions = 1;
  repeated ActionFeature features = 2;
}
