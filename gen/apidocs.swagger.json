{"swagger": "2.0", "info": {"title": "proto/server/v1/server.proto", "version": "version not set"}, "tags": [{"name": "Manager"}, {"name": "TaskManager"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/agent/healthz": {"get": {"summary": "健康检查", "operationId": "TaskManager_Healthz", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufHealthzResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["TaskManager"]}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/remove": {"post": {"summary": "执行主机移除", "operationId": "Manager_RemoveHost", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufRemoveHostResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "type": "string"}, {"name": "host_uuid", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ManagerRemoveHostBody"}}], "tags": ["Manager"]}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/remove_check": {"post": {"summary": "主机移除前置检查", "operationId": "Manager_RemoveHostCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufRemoveHostCheckResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "type": "string"}, {"name": "host_uuid", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ManagerRemoveHostCheckBody"}}], "tags": ["Manager"]}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/remove_check_result": {"get": {"summary": "获取主机移除前置检查结果", "operationId": "Manager_RemoveHostCheckResult", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufCheckResultResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "type": "string"}, {"name": "host_uuid", "in": "path", "required": true, "type": "string"}, {"name": "client_id", "in": "query", "required": false, "type": "string"}, {"name": "job_id", "in": "query", "required": false, "type": "string"}], "tags": ["Manager"]}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/role_convert": {"post": {"summary": "执行角色转换", "operationId": "Manager_ConvertRole", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufConvertRoleResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "type": "string"}, {"name": "host_uuid", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ManagerConvertRoleBody"}}], "tags": ["Manager"]}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/role_convert_check": {"post": {"summary": "角色转换前置检查", "operationId": "Manager_ConvertR<PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufConvertRoleCheckResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "cluster_uuid", "description": "Target Host cluster uuid", "in": "path", "required": true, "type": "string"}, {"name": "host_uuid", "description": "Target host uuid", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ManagerConvertRoleCheckBody"}}], "tags": ["Manager"]}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/role_convert_check_result": {"get": {"summary": "获取角色转换前置检查结果", "operationId": "Manager_ConvertRoleCheckResult", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufCheckResultResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "type": "string"}, {"name": "host_uuid", "in": "path", "required": true, "type": "string"}, {"name": "client_id", "in": "query", "required": false, "type": "string"}, {"name": "job_id", "in": "query", "required": false, "type": "string"}], "tags": ["Manager"]}}, "/api/v1/clusters/{cluster_uuid}/rdma/toggle": {"post": {"summary": "开启或关闭 RDMA", "operationId": "Manager_SetRDMA", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufSetRDMAResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ManagerSetRDMABody"}}], "tags": ["Manager"]}}, "/api/v1/clusters/{cluster_uuid}/time/sync": {"post": {"summary": "同步集群时间", "operationId": "Manager_TimeSync", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufTimeSyncResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ManagerTimeSyncBody"}}], "tags": ["Manager"]}}, "/api/v1/healthz": {"get": {"summary": "健康检查", "operationId": "Manager_Healthz", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufHealthzResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["Manager"]}}, "/api/v1/jobs": {"get": {"summary": "列出所有 Jobs", "operationId": "Manager_ListJobs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufListJobsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "filter", "in": "query", "required": false, "type": "string"}, {"name": "order_by", "in": "query", "required": false, "type": "string"}, {"name": "page_size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page_token", "in": "query", "required": false, "type": "string"}], "tags": ["Manager"]}}, "/api/v1/jobs/{id}": {"get": {"summary": "获取 Job 详情", "operationId": "Manager_<PERSON><PERSON><PERSON>", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufJob"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "pattern": "[^/]+"}], "tags": ["Manager"]}}, "/api/v1/jobs/{id}/logs": {"get": {"summary": "获取 Job 日志", "operationId": "Manager_GetJobLogs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufJobLogs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "pattern": "[^/]+"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "length", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["Manager"]}}, "/api/v1/supported_actions": {"get": {"summary": "获取支持集群变更白名单", "operationId": "Manager_GetSupportedActions", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufAvailabilityMap"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["Manager"]}}, "/api/v1/tasks": {"get": {"summary": "获取 LCM Manager Agent 的 Task 列表", "operationId": "TaskManager_ListTasks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufListTasksReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["TaskManager"]}, "post": {"summary": "创建 LCM Manager Agent Task", "operationId": "TaskManager_CreateTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufCreateTaskReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/protobufTaskInput"}}], "tags": ["TaskManager"]}}, "/api/v1/tasks/{task_id}": {"get": {"summary": "获取 LCM Manager Agent 的 Task 信息", "operationId": "TaskManager_GetTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protobufGetTaskReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "task_id", "in": "path", "required": true, "type": "string"}], "tags": ["TaskManager"]}}}, "definitions": {"ManagerConvertRoleBody": {"type": "object", "properties": {"client_id": {"type": "string"}, "current_role": {"$ref": "#/definitions/protobufHostRole"}, "target_role": {"$ref": "#/definitions/protobufHostRole"}, "skip_precheck": {"type": "boolean"}}}, "ManagerConvertRoleCheckBody": {"type": "object", "properties": {"client_id": {"type": "string", "title": "client id for idempotent request"}, "current_role": {"$ref": "#/definitions/protobufHostRole", "title": "current host role"}, "target_role": {"$ref": "#/definitions/protobufHostRole", "title": "target host role"}}, "title": "ConvertRoleCheckRequest"}, "ManagerRemoveHostBody": {"type": "object", "properties": {"client_id": {"type": "string"}, "skip_precheck": {"type": "boolean"}}}, "ManagerRemoveHostCheckBody": {"type": "object", "properties": {"client_id": {"type": "string"}}}, "ManagerSetRDMABody": {"type": "object", "properties": {"client_id": {"type": "string"}, "state": {"$ref": "#/definitions/protobufRDMAState"}}}, "ManagerTimeSyncBody": {"type": "object", "properties": {"client_id": {"type": "string"}, "time": {"type": "string"}}}, "lcmagentprotobufErrorCode": {"type": "string", "enum": ["EC_UNSPECIFIED", "EC_EOK", "EC_INVALID_PARAMETER", "EC_CREATE_TASK_FAILED", "EC_UNAUTHORIZED", "EC_NOT_FOUND"], "default": "EC_UNSPECIFIED"}, "lcmmanagerprotobufErrorCode": {"type": "string", "enum": ["EC_UNSPECIFIED", "SECOND_PRE_CHECK_FAILED"], "default": "EC_UNSPECIFIED"}, "protobufAction": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"$ref": "#/definitions/protobufActionType"}, "versioned_actions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufVersionedAction"}}}}, "protobufActionFeature": {"type": "string", "enum": ["ACTION_FEATURE_UNSPECIFIED", "SUPPORT_MULTI_OFFLINE_HOSTS"], "default": "ACTION_FEATURE_UNSPECIFIED"}, "protobufActionType": {"type": "string", "enum": ["ACTION_TYPE_UNSPECIFIED", "CONVERT_ROLE", "REMOVE_HOST", "TIME_SYNC"], "default": "ACTION_TYPE_UNSPECIFIED"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "protobufAvailabilityMap": {"type": "object", "properties": {"version": {"type": "string"}, "product_actions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufProductAction"}}}}, "protobufCheckResult": {"type": "object", "properties": {"id": {"type": "string"}, "job_id": {"type": "string"}, "check_name": {"type": "string"}, "description": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufMessageItem"}}, "state": {"$ref": "#/definitions/protobufCheckState"}, "messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufMessageItem"}}, "create_time": {"type": "string", "format": "date-time"}, "update_time": {"type": "string", "format": "date-time"}}}, "protobufCheckResultResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}, "job": {"$ref": "#/definitions/protobufJob"}, "check_results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufCheckResult"}}}}, "protobufCheckState": {"type": "string", "enum": ["CHECK_STATE_UNSPECIFIED", "CHECK_STATE_PENDING", "CHECK_STATE_RUNNING", "CHECK_STATE_SUCCESS", "CHECK_STATE_FAILED", "CHECK_STATE_WARNING"], "default": "CHECK_STATE_UNSPECIFIED"}, "protobufConvertRoleCheckResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufConvertRoleResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufCreateTaskReply": {"type": "object", "properties": {"ec": {"$ref": "#/definitions/lcmagentprotobufErrorCode"}, "data": {"$ref": "#/definitions/protobufCreateTaskReplyData"}, "error": {"type": "string"}}}, "protobufCreateTaskReplyData": {"type": "object", "properties": {"task_id": {"type": "string"}}}, "protobufGetTaskReply": {"type": "object", "properties": {"ec": {"$ref": "#/definitions/lcmagentprotobufErrorCode"}, "data": {"$ref": "#/definitions/protobufTask"}, "error": {"type": "string"}}}, "protobufHealthzResponse": {"type": "object", "properties": {"result": {"type": "string"}}}, "protobufHostRole": {"type": "string", "enum": ["ROLE_UNSPECIFIED", "ROLE_MASTER", "ROLE_STORAGE"], "default": "ROLE_UNSPECIFIED"}, "protobufJob": {"type": "object", "properties": {"id": {"type": "string"}, "cluster_uuid": {"type": "string"}, "host_uuid": {"type": "string"}, "name": {"type": "string"}, "state": {"$ref": "#/definitions/protobufJobState"}, "details": {"type": "object", "additionalProperties": {"type": "string"}}, "progress": {"$ref": "#/definitions/protobufJobProgress"}, "messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufMessageItem"}}, "ec": {"$ref": "#/definitions/lcmmanagerprotobufErrorCode"}, "create_time": {"type": "string", "format": "date-time"}, "update_time": {"type": "string", "format": "date-time"}}}, "protobufJobLogs": {"type": "object", "properties": {"logs": {"type": "array", "items": {"type": "string"}}, "offset": {"type": "integer", "format": "int32"}, "next_offset": {"type": "integer", "format": "int32"}}}, "protobufJobProgress": {"type": "object", "properties": {"progress": {"type": "string"}, "total_time": {"type": "string"}, "details": {"$ref": "#/definitions/protobufProgressDetail"}}}, "protobufJobState": {"type": "string", "enum": ["JOB_STATE_UNSPECIFIED", "JOB_STATE_PENDING", "JOB_STATE_RUNNING", "JOB_STATE_SUCCESS", "JOB_STATE_FAILED"], "default": "JOB_STATE_UNSPECIFIED"}, "protobufListJobsResponse": {"type": "object", "properties": {"jobs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufJob"}}, "next_page_token": {"type": "string"}, "total": {"type": "string"}}}, "protobufListTasksReply": {"type": "object", "properties": {"ec": {"$ref": "#/definitions/lcmagentprotobufErrorCode"}, "data": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufTask"}}, "error": {"type": "string"}}}, "protobufMessageItem": {"type": "object", "properties": {"lang": {"$ref": "#/definitions/protobufMessageLang"}, "message": {"type": "string"}}}, "protobufMessageLang": {"type": "string", "enum": ["LANG_UNSPECIFIED", "LANG_EN", "LANG_ZH"], "default": "LANG_UNSPECIFIED"}, "protobufProductAction": {"type": "object", "properties": {"product_name": {"$ref": "#/definitions/protobufProductName"}, "actions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAction"}}}}, "protobufProductName": {"type": "string", "enum": ["PRODUCT_UNSPECIFIED", "SMTXOS", "SMTXZBS", "SMTXELF"], "default": "PRODUCT_UNSPECIFIED"}, "protobufProgressDetail": {"type": "object", "properties": {"name": {"type": "string"}, "items": {"type": "object", "additionalProperties": {"type": "string"}}}}, "protobufQA": {"type": "object", "properties": {"query": {"type": "string"}, "answer": {"type": "string"}, "passed_mark": {"type": "string"}}}, "protobufRDMAState": {"type": "string", "enum": ["RDMA_UNKNOWN", "RDMA_ON", "RDMA_OFF"], "default": "RDMA_UNKNOWN"}, "protobufRemoveHostCheckResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufRemoveHostResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufSetRDMAResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufTask": {"type": "object", "properties": {"task_id": {"type": "string"}, "input": {"$ref": "#/definitions/protobufTaskInput"}, "output": {"$ref": "#/definitions/protobufTaskOutput"}, "status": {"$ref": "#/definitions/protobufTaskStatus"}}}, "protobufTaskInput": {"type": "object", "properties": {"task_id": {"type": "string"}, "command": {"type": "string"}, "target_ip": {"type": "string"}, "timeout": {"type": "integer", "format": "int32"}, "cmd_qa": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufQA"}}}}, "protobufTaskOutput": {"type": "object", "properties": {"stdout": {"type": "string"}, "stderr": {"type": "string"}}}, "protobufTaskStatus": {"type": "object", "properties": {"status": {"$ref": "#/definitions/protobufTaskStatusEnum"}, "error": {"type": "string"}, "start_at": {"type": "string", "format": "date-time"}, "end_at": {"type": "string", "format": "date-time"}}}, "protobufTaskStatusEnum": {"type": "string", "enum": ["UNSPECIFIED", "FAILED", "PENDING", "COMPLETED", "RUNNING"], "default": "UNSPECIFIED"}, "protobufTimeSyncResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufVersionedAction": {"type": "object", "properties": {"versions": {"type": "array", "items": {"type": "string"}}, "features": {"type": "array", "items": {"$ref": "#/definitions/protobufActionFeature"}}}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}