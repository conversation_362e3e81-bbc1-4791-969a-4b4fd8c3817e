{"openapi": "3.0.1", "info": {"title": "proto/server/v1/server.proto", "version": "version not set"}, "servers": [{"url": "/"}], "tags": [{"name": "Manager"}, {"name": "TaskManager"}], "paths": {"/api/v1/agent/healthz": {"get": {"tags": ["TaskManager"], "summary": "健康检查", "operationId": "TaskManager_Healthz", "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufHealthzResponse"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/remove": {"post": {"tags": ["Manager"], "summary": "执行主机移除", "operationId": "Manager_RemoveHost", "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "host_uuid", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManagerRemoveHostBody"}}}, "required": true}, "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufRemoveHostResponse"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}, "x-codegen-request-body-name": "body"}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/remove_check": {"post": {"tags": ["Manager"], "summary": "主机移除前置检查", "operationId": "Manager_RemoveHostCheck", "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "host_uuid", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManagerRemoveHostCheckBody"}}}, "required": true}, "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufRemoveHostCheckResponse"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}, "x-codegen-request-body-name": "body"}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/remove_check_result": {"get": {"tags": ["Manager"], "summary": "获取主机移除前置检查结果", "operationId": "Manager_RemoveHostCheckResult", "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "host_uuid", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "job_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufCheckResultResponse"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/role_convert": {"post": {"tags": ["Manager"], "summary": "执行角色转换", "operationId": "Manager_ConvertRole", "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "host_uuid", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManagerConvertRoleBody"}}}, "required": true}, "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufConvertRoleResponse"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}, "x-codegen-request-body-name": "body"}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/role_convert_check": {"post": {"tags": ["Manager"], "summary": "角色转换前置检查", "operationId": "Manager_ConvertR<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "cluster_uuid", "in": "path", "description": "Target Host cluster uuid", "required": true, "schema": {"type": "string"}}, {"name": "host_uuid", "in": "path", "description": "Target host uuid", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManagerConvertRoleCheckBody"}}}, "required": true}, "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufConvertRoleCheckResponse"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}, "x-codegen-request-body-name": "body"}}, "/api/v1/clusters/{cluster_uuid}/hosts/{host_uuid}/role_convert_check_result": {"get": {"tags": ["Manager"], "summary": "获取角色转换前置检查结果", "operationId": "Manager_ConvertRoleCheckResult", "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "host_uuid", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "job_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufCheckResultResponse"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}}}, "/api/v1/clusters/{cluster_uuid}/rdma/toggle": {"post": {"tags": ["Manager"], "summary": "开启或关闭 RDMA", "operationId": "Manager_SetRDMA", "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManagerSetRDMABody"}}}, "required": true}, "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufSetRDMAResponse"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}, "x-codegen-request-body-name": "body"}}, "/api/v1/clusters/{cluster_uuid}/time/sync": {"post": {"tags": ["Manager"], "summary": "同步集群时间", "operationId": "Manager_TimeSync", "parameters": [{"name": "cluster_uuid", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManagerTimeSyncBody"}}}, "required": true}, "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufTimeSyncResponse"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}, "x-codegen-request-body-name": "body"}}, "/api/v1/healthz": {"get": {"tags": ["Manager"], "summary": "健康检查", "operationId": "Manager_Healthz", "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufHealthzResponse"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}}}, "/api/v1/jobs": {"get": {"tags": ["Manager"], "summary": "列出所有 Jobs", "operationId": "Manager_ListJobs", "parameters": [{"name": "filter", "in": "query", "schema": {"type": "string"}}, {"name": "order_by", "in": "query", "schema": {"type": "string"}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page_token", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufListJobsResponse"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}}}, "/api/v1/jobs/{id}": {"get": {"tags": ["Manager"], "summary": "获取 Job 详情", "operationId": "Manager_<PERSON><PERSON><PERSON>", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"pattern": "[^/]+", "type": "string"}}], "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufJob"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}}}, "/api/v1/jobs/{id}/logs": {"get": {"tags": ["Manager"], "summary": "获取 Job 日志", "operationId": "Manager_GetJobLogs", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"pattern": "[^/]+", "type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "length", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufJobLogs"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}}}, "/api/v1/supported_actions": {"get": {"tags": ["Manager"], "summary": "获取支持集群变更白名单", "operationId": "Manager_GetSupportedActions", "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufAvailabilityMap"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}}}, "/api/v1/tasks": {"get": {"tags": ["TaskManager"], "summary": "获取 LCM Manager Agent 的 Task 列表", "operationId": "TaskManager_ListTasks", "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufListTasksReply"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}}, "post": {"tags": ["TaskManager"], "summary": "创建 LCM Manager Agent Task", "operationId": "TaskManager_CreateTask", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufTaskInput"}}}, "required": true}, "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufCreateTaskReply"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}, "x-codegen-request-body-name": "body"}}, "/api/v1/tasks/{task_id}": {"get": {"tags": ["TaskManager"], "summary": "获取 LCM Manager Agent 的 Task 信息", "operationId": "TaskManager_GetTask", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A successful response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/protobufGetTaskReply"}}}}, "default": {"description": "An unexpected error response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/rpcStatus"}}}}}}}}, "components": {"schemas": {"ManagerConvertRoleBody": {"type": "object", "properties": {"client_id": {"type": "string"}, "current_role": {"$ref": "#/components/schemas/protobufHostRole"}, "target_role": {"$ref": "#/components/schemas/protobufHostRole"}, "skip_precheck": {"type": "boolean"}}}, "ManagerConvertRoleCheckBody": {"title": "ConvertRoleCheckRequest", "type": "object", "properties": {"client_id": {"title": "client id for idempotent request", "type": "string"}, "current_role": {"$ref": "#/components/schemas/protobufHostRole"}, "target_role": {"$ref": "#/components/schemas/protobufHostRole"}}}, "ManagerRemoveHostBody": {"type": "object", "properties": {"client_id": {"type": "string"}, "skip_precheck": {"type": "boolean"}}}, "ManagerRemoveHostCheckBody": {"type": "object", "properties": {"client_id": {"type": "string"}}}, "ManagerSetRDMABody": {"type": "object", "properties": {"client_id": {"type": "string"}, "state": {"$ref": "#/components/schemas/protobufRDMAState"}}}, "ManagerTimeSyncBody": {"type": "object", "properties": {"client_id": {"type": "string"}, "time": {"type": "string"}}}, "lcmagentprotobufErrorCode": {"type": "string", "default": "EC_UNSPECIFIED", "enum": ["EC_UNSPECIFIED", "EC_EOK", "EC_INVALID_PARAMETER", "EC_CREATE_TASK_FAILED", "EC_UNAUTHORIZED", "EC_NOT_FOUND"]}, "lcmmanagerprotobufErrorCode": {"type": "string", "default": "EC_UNSPECIFIED", "enum": ["EC_UNSPECIFIED", "SECOND_PRE_CHECK_FAILED"]}, "protobufAction": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"$ref": "#/components/schemas/protobufActionType"}, "versioned_actions": {"type": "array", "items": {"$ref": "#/components/schemas/protobufVersionedAction"}}}}, "protobufActionFeature": {"type": "string", "default": "ACTION_FEATURE_UNSPECIFIED", "enum": ["ACTION_FEATURE_UNSPECIFIED", "SUPPORT_MULTI_OFFLINE_HOSTS"]}, "protobufActionType": {"type": "string", "default": "ACTION_TYPE_UNSPECIFIED", "enum": ["ACTION_TYPE_UNSPECIFIED", "CONVERT_ROLE", "REMOVE_HOST", "TIME_SYNC"]}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {"type": "object"}}, "protobufAvailabilityMap": {"type": "object", "properties": {"version": {"type": "string"}, "product_actions": {"type": "array", "items": {"$ref": "#/components/schemas/protobufProductAction"}}}}, "protobufCheckResult": {"type": "object", "properties": {"id": {"type": "string"}, "job_id": {"type": "string"}, "check_name": {"type": "string"}, "description": {"type": "array", "items": {"$ref": "#/components/schemas/protobufMessageItem"}}, "state": {"$ref": "#/components/schemas/protobufCheckState"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/protobufMessageItem"}}, "create_time": {"type": "string", "format": "date-time"}, "update_time": {"type": "string", "format": "date-time"}}}, "protobufCheckResultResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}, "job": {"$ref": "#/components/schemas/protobufJob"}, "check_results": {"type": "array", "items": {"$ref": "#/components/schemas/protobufCheckResult"}}}}, "protobufCheckState": {"type": "string", "default": "CHECK_STATE_UNSPECIFIED", "enum": ["CHECK_STATE_UNSPECIFIED", "CHECK_STATE_PENDING", "CHECK_STATE_RUNNING", "CHECK_STATE_SUCCESS", "CHECK_STATE_FAILED", "CHECK_STATE_WARNING"]}, "protobufConvertRoleCheckResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufConvertRoleResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufCreateTaskReply": {"type": "object", "properties": {"ec": {"$ref": "#/components/schemas/lcmagentprotobufErrorCode"}, "data": {"$ref": "#/components/schemas/protobufCreateTaskReplyData"}, "error": {"type": "string"}}}, "protobufCreateTaskReplyData": {"type": "object", "properties": {"task_id": {"type": "string"}}}, "protobufGetTaskReply": {"type": "object", "properties": {"ec": {"$ref": "#/components/schemas/lcmagentprotobufErrorCode"}, "data": {"$ref": "#/components/schemas/protobufTask"}, "error": {"type": "string"}}}, "protobufHealthzResponse": {"type": "object", "properties": {"result": {"type": "string"}}}, "protobufHostRole": {"type": "string", "default": "ROLE_UNSPECIFIED", "enum": ["ROLE_UNSPECIFIED", "ROLE_MASTER", "ROLE_STORAGE"]}, "protobufJob": {"type": "object", "properties": {"id": {"type": "string"}, "cluster_uuid": {"type": "string"}, "host_uuid": {"type": "string"}, "name": {"type": "string"}, "state": {"$ref": "#/components/schemas/protobufJobState"}, "details": {"type": "object", "additionalProperties": {"type": "string"}}, "progress": {"$ref": "#/components/schemas/protobufJobProgress"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/protobufMessageItem"}}, "ec": {"$ref": "#/components/schemas/lcmmanagerprotobufErrorCode"}, "create_time": {"type": "string", "format": "date-time"}, "update_time": {"type": "string", "format": "date-time"}}}, "protobufJobLogs": {"type": "object", "properties": {"logs": {"type": "array", "items": {"type": "string"}}, "offset": {"type": "integer", "format": "int32"}, "next_offset": {"type": "integer", "format": "int32"}}}, "protobufJobProgress": {"type": "object", "properties": {"progress": {"type": "string"}, "total_time": {"type": "string"}, "details": {"$ref": "#/components/schemas/protobufProgressDetail"}}}, "protobufJobState": {"type": "string", "default": "JOB_STATE_UNSPECIFIED", "enum": ["JOB_STATE_UNSPECIFIED", "JOB_STATE_PENDING", "JOB_STATE_RUNNING", "JOB_STATE_SUCCESS", "JOB_STATE_FAILED"]}, "protobufListJobsResponse": {"type": "object", "properties": {"jobs": {"type": "array", "items": {"$ref": "#/components/schemas/protobufJob"}}, "next_page_token": {"type": "string"}, "total": {"type": "string"}}}, "protobufListTasksReply": {"type": "object", "properties": {"ec": {"$ref": "#/components/schemas/lcmagentprotobufErrorCode"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/protobufTask"}}, "error": {"type": "string"}}}, "protobufMessageItem": {"type": "object", "properties": {"lang": {"$ref": "#/components/schemas/protobufMessageLang"}, "message": {"type": "string"}}}, "protobufMessageLang": {"type": "string", "default": "LANG_UNSPECIFIED", "enum": ["LANG_UNSPECIFIED", "LANG_EN", "LANG_ZH"]}, "protobufProductAction": {"type": "object", "properties": {"product_name": {"$ref": "#/components/schemas/protobufProductName"}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/protobufAction"}}}}, "protobufProductName": {"type": "string", "default": "PRODUCT_UNSPECIFIED", "enum": ["PRODUCT_UNSPECIFIED", "SMTXOS", "SMTXZBS", "SMTXELF"]}, "protobufProgressDetail": {"type": "object", "properties": {"name": {"type": "string"}, "items": {"type": "object", "additionalProperties": {"type": "string"}}}}, "protobufQA": {"type": "object", "properties": {"query": {"type": "string"}, "answer": {"type": "string"}, "passed_mark": {"type": "string"}}}, "protobufRDMAState": {"type": "string", "default": "RDMA_UNKNOWN", "enum": ["RDMA_UNKNOWN", "RDMA_ON", "RDMA_OFF"]}, "protobufRemoveHostCheckResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufRemoveHostResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufSetRDMAResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufTask": {"type": "object", "properties": {"task_id": {"type": "string"}, "input": {"$ref": "#/components/schemas/protobufTaskInput"}, "output": {"$ref": "#/components/schemas/protobufTaskOutput"}, "status": {"$ref": "#/components/schemas/protobufTaskStatus"}}}, "protobufTaskInput": {"type": "object", "properties": {"task_id": {"type": "string"}, "command": {"type": "string"}, "target_ip": {"type": "string"}, "timeout": {"type": "integer", "format": "int32"}, "cmd_qa": {"type": "array", "items": {"$ref": "#/components/schemas/protobufQA"}}}}, "protobufTaskOutput": {"type": "object", "properties": {"stdout": {"type": "string"}, "stderr": {"type": "string"}}}, "protobufTaskStatus": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/protobufTaskStatusEnum"}, "error": {"type": "string"}, "start_at": {"type": "string", "format": "date-time"}, "end_at": {"type": "string", "format": "date-time"}}}, "protobufTaskStatusEnum": {"type": "string", "default": "UNSPECIFIED", "enum": ["UNSPECIFIED", "FAILED", "PENDING", "COMPLETED", "RUNNING"]}, "protobufTimeSyncResponse": {"type": "object", "properties": {"client_id": {"type": "string"}, "job_id": {"type": "string"}}}, "protobufVersionedAction": {"type": "object", "properties": {"versions": {"type": "array", "items": {"type": "string"}}, "features": {"type": "array", "items": {"$ref": "#/components/schemas/protobufActionFeature"}}}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/protobufAny"}}}}}}, "x-original-swagger-version": "2.0"}