version: 1.0.0
product_actions:
  - product_name: SMTXOS
    actions:
      - name: CONVERT_TO_STORAGE
        type: CONVERT_ROLE
        versioned_actions:
          - versions: [">= 5.1.4, < 6.0.0", ">= 6.1.0"]
            features:
              - SUPPORT_MULTI_OFFLINE_HOSTS
            agent_cmds: &convert_to_storage_agent_cmds
              - name: convert_to_storage
                command: "sudo zbs-cluster convert_to_storage --no_restart_service"
                timeout_factor: 600
                cmd_qa:
                  - query: "This node role is not master. Do you want to continue?"
                    answer: "yes"
                    passed_mark: "Sync cluster ips to /etc/zbs/zbs.conf"
              - name: restart_service
                command: "sudo /usr/share/tuna/script/role_convert/restart_services.sh"
                timeout_factor: 600
      - name: CONVERT_TO_MASTER
        type: CONVERT_ROLE
        versioned_actions:
          - versions: [">= 5.1.4, < 6.0.0", ">= 6.1.0"]
            features: []
            agent_cmds: &convert_to_master_agent_cmds
              - name: convert_to_master
                command: "sudo zbs-cluster convert_to_master --no_restart_service"
                timeout_factor: 600
                cmd_qa:
                  - query: "Do you want to continue?"
                    answer: "yes"
                    passed_mark: "Sync cluster ips to /etc/zbs/zbs.conf"
              - name: restart_service
                command: "sudo /usr/share/tuna/script/role_convert/restart_services.sh"
                timeout_factor: 600

      - name: REMOVE_HOST
        type: REMOVE_HOST
        versioned_actions:
          - versions: [">= 5.1.4, < 6.0.0", ">= 6.1.0"]
            features:
              - SUPPORT_MULTI_OFFLINE_HOSTS
            agent_cmds: &remove_host_agent_cmds
              - name: storage_pool_remove_node
                command: "sudo zbs-deploy-manage storage_pool_remove_node"
                timeout_factor: 120
                cmd_qa:
                  - query: "Please input the management ip of the host:"
                    answer: "{{management_ip}}"
                  - query: "The cluster is ok to remove the node, are you sure to continue?"
                    answer: "y"
                  - query: "are you sure there is no running virtual machine on the host?"
                    answer: "y"
                  # TUNA-7600 updated query message, so keep the old and new query message
                  - query: "are you sure there is no virtual machine on the host?"
                    answer: "y"
              - name: meta_remove_node
                command: "sudo zbs-deploy-manage meta_remove_node"
                timeout_factor: 120
                cmd_qa:
                  - query: "Please input the management ip of the host:"
                    answer: "{{management_ip}}"
                  - query: "The cluster is ok to remove the node, are you sure to continue?"
                    answer: "y"
                  - query: "are you sure there is no running virtual machine on the host?"
                    answer: "y"
                  # TUNA-7600 updated query message, so keep the old and new query message
                  - query: "are you sure there is no virtual machine on the host?"
                    answer: "y"

      - name: TIME_SYNC
        type: TIME_SYNC
        versioned_actions:
          - versions: [">= 4.0.11, < 6.0.0", ">= 5.0.1, < 6.0.0"]
            agent_cmds: &time_sync_agent_cmds_legacy
              - name: time_sync_with_ntp_server
                command: sudo zbs-cluster sync_time_with_external_ntp
                timeout_factor: 900
              - name: time_sync_internal
                command: sudo zbs-cluster sync_internal_leader_to_normal --time "{{time}}"
                timeout_factor: 900
          - versions: [">= 6.0.0"]
            agent_cmds: &time_sync_agent_cmds
              - name: time_sync_with_ntp_server
                command: sudo zbs-cluster sync_time
                timeout_factor: 900
              - name: time_sync_internal
                command: sudo zbs-cluster sync_internal_leader_to_normal --time "{{time}}"
                timeout_factor: 900

      - name: RDMA_TOGGLE
        type: RDMA_TOGGLE
        versioned_actions:
          - versions: [">= 6.0.0"]
            agent_cmds: &rdma_toggle_agent_cmds
              - name: rdma_precheck
                command: echo "precheck rdma config"
                timeout_factor: 300
              - name: rdma_config
                command: echo "modify rdma config"
                timeout_factor: 300
              - name: restart_service
                command: echo "restart service"
                timeout_factor: 300

  - product_name: SMTXELF
    actions:
      - name: CONVERT_TO_STORAGE
        type: CONVERT_ROLE
        versioned_actions:
          - versions: [">= 6.1.0"]
            features:
              - SUPPORT_MULTI_OFFLINE_HOSTS
            agent_cmds: *convert_to_storage_agent_cmds
      - name: CONVERT_TO_MASTER
        type: CONVERT_ROLE
        versioned_actions:
          - versions: [">= 6.1.0"]
            features: []
            agent_cmds: *convert_to_master_agent_cmds

      - name: REMOVE_HOST
        type: REMOVE_HOST
        versioned_actions:
          - versions: [">= 6.1.0"]
            features:
              - SUPPORT_MULTI_OFFLINE_HOSTS
            agent_cmds: *remove_host_agent_cmds

      - name: TIME_SYNC
        type: TIME_SYNC
        versioned_actions:
          - versions: [">= 6.0.0"]
            agent_cmds: *time_sync_agent_cmds

  - product_name: SMTXZBS
    actions:
      - name: CONVERT_TO_STORAGE
        type: CONVERT_ROLE
        versioned_actions:
          - versions: [">= 5.7.0"]
            features:
              - SUPPORT_MULTI_OFFLINE_HOSTS
            agent_cmds: *convert_to_storage_agent_cmds
      - name: CONVERT_TO_MASTER
        type: CONVERT_ROLE
        versioned_actions:
          - versions: [">= 5.7.0"]
            features: []
            agent_cmds: *convert_to_master_agent_cmds

      - name: REMOVE_HOST
        type: REMOVE_HOST
        versioned_actions:
          - versions: [">= 5.7.0"]
            features:
              - SUPPORT_MULTI_OFFLINE_HOSTS
            agent_cmds: *remove_host_agent_cmds

      - name: TIME_SYNC
        type: TIME_SYNC
        versioned_actions:
          - versions: [">= 5.1.0, < 5.6.0"]
            agent_cmds: *time_sync_agent_cmds_legacy
          - versions: [">= 5.6.0"]
            agent_cmds: *time_sync_agent_cmds
