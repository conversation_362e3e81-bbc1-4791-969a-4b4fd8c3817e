package utils

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"os"
	"slices"
	"sort"
	"strings"
	"time"

	"github.com/eduardolat/goeasyi18n"
	"github.com/gofrs/uuid"
	"github.com/hashicorp/go-version"
	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gopkg.in/yaml.v2"

	agentpb "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	serverpb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	agentclient "github.smartx.com/LCM/lcm-manager/pkg/client/agent"
	zbsclient "github.smartx.com/LCM/lcm-manager/pkg/client/zbs"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	"github.smartx.com/LCM/lcm-manager/pkg/hostplugin"
	"github.smartx.com/LCM/lcm-manager/pkg/server/postgres"
	hpoperator "github.smartx.com/LCM/lcm-manager/third_party/host_plugin_operator"
	"github.smartx.com/LCM/lcm-manager/third_party/tower"
	"github.smartx.com/LCM/lcm-manager/third_party/tower/schema"
	"github.smartx.com/LCM/lcm-manager/third_party/tuna"
)

type CustomedHeader struct {
	UserID string `json:"user_id"`
	UserIP string `json:"user_ip"`
}

type TowerTaskConfig struct {
	Internal         bool
	ResourceMutation string
	AuditEventType   string
	TaskName         string // name in i18n config
	AuditEventName   string // name in i18n config
}

var TowerTaskConfigs = map[string]TowerTaskConfig{
	"RoleConvertCheck": {
		Internal:         true,
		ResourceMutation: string(config.TowerTaskResourceMutationRoleConvertPrecheck),
		TaskName:         "RoleConvertCheckTowerTask",
	},
	"RoleConvert": {
		Internal:         false,
		ResourceMutation: string(config.TowerTaskResourceMutationRoleConvert),
		AuditEventType:   string(config.TowerAuditEventTypeRoleConvert),
		TaskName:         "RoleConvertTowerTask",
		AuditEventName:   "RoleConvertTowerAuditEvent",
	},
	"RemoveHostCheck": {
		Internal:         true,
		ResourceMutation: string(config.TowerTaskResourceMutationHostRemovePrecheck),
		TaskName:         "RemoveHostCheckTowerTask",
	},
	"RemoveHost": {
		Internal:         false,
		ResourceMutation: string(config.TowerTaskResourceMutationHostRemove),
		AuditEventType:   string(config.TowerAuditEventTypeHostRemove),
		TaskName:         "RemoveHostTowerTask",
		AuditEventName:   "RemoveHostTowerAuditEvent",
	},
	"TimeSyncWithNtpServer": {
		Internal:         false,
		ResourceMutation: string(config.TowerTaskResourceMutationTimeSync),
		AuditEventType:   string(config.TowerAuditEventTypeTimeSync),
		TaskName:         "TimeSyncWithNtpServerTowerTask",
		AuditEventName:   "TimeSyncWithNtpServerTowerAuditEvent",
	},
	"TimeSyncInternal": {
		Internal:         false,
		ResourceMutation: string(config.TowerTaskResourceMutationTimeSync),
		AuditEventType:   string(config.TowerAuditEventTypeTimeSync),
		TaskName:         "TimeSyncInternalTowerTask",
		AuditEventName:   "TimeSyncInternalTowerAuditEvent",
	},
}

func LoadActivityConfigs() (config.ActivityConfigs, error) {
	slog.Info("read activity configs file", "file_name", config.ActivityConfigFile)

	fileData, err := os.ReadFile(config.ActivityConfigFile)
	if err != nil {
		slog.Error("fail to read yaml file.", "file", config.ActivityConfigFile, "error", err)
		return nil, err
	}

	var activityConfigs config.ActivityConfigs

	err = yaml.Unmarshal(fileData, &activityConfigs)
	if err != nil {
		slog.Error("fail to unmarshal yaml", "error", err)
		return nil, err
	}

	return activityConfigs, nil
}

func GetContextWithActivityOptions(ctx workflow.Context, name config.ActivityOptionsName) (workflow.Context, error) {
	slog.Info("start get context with activity options", "options_name", name)

	activityConfigs, err := LoadActivityConfigs()
	if err != nil {
		return ctx, err
	}

	aoData, ok := activityConfigs[name]
	if !ok {
		return ctx, fmt.Errorf("fail to get activity options config with name %s", name)
	}

	slog.Info("load activity options configs data", "data", aoData)

	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Duration(aoData.StartToCloseTimeout) * time.Second,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Duration(aoData.RetryInitialInterval) * time.Second,
			BackoffCoefficient: aoData.RetryBackoffCoefficient,
			MaximumInterval:    time.Duration(aoData.RetryMaximumInterval) * time.Second,
			MaximumAttempts:    int32(aoData.RetryMaximumAttempts),
		},
	}

	return workflow.WithActivityOptions(ctx, ao), nil
}

func CreateJob(ctx context.Context, jobRepository postgres.IJobRepository, clusterUUID string, hostUUID string, jobName string) (string, error) {
	slog.Info("start create job in db", "name", jobName)

	// use uuidv7 as job id
	jobID, err := uuid.NewV7()
	if err != nil {
		slog.Error("fail to generate uuid wile creating job", "error", err)
		return "", err
	}

	now := time.Now()
	job := &serverpb.Job{
		Id:          jobID.String(),
		ClusterUuid: clusterUUID,
		HostUuid:    hostUUID,
		Name:        jobName,
		State:       serverpb.JobState_JOB_STATE_PENDING,
		CreateTime:  &timestamppb.Timestamp{Seconds: now.Unix()},
		UpdateTime:  &timestamppb.Timestamp{Seconds: now.Unix()},
	}

	err = jobRepository.Save(ctx, job)
	if err != nil {
		slog.Error("fail to create job in db", "name", jobName, "error", err)
		return "", err
	}

	slog.Info("finish create job in db", "name", jobName, "job_id", jobID)

	return jobID.String(), nil
}

func PickHostName(hostName string, scvmHostName string, isVmware bool) string {
	if isVmware {
		return scvmHostName
	}

	return hostName
}

func GetZhAndEnI18nLanguageName(isVmware bool) (string, string) {
	langEn := "en"
	langZh := "zh"

	if isVmware {
		langEn = "en-vmware"
		langZh = "zh-vmware"
	}

	return langZh, langEn
}

func GetHostLabelValue(tunaClient *tuna.Client, hostUUID string, labelKey string) (config.HostLabel, error) {
	res, err := tunaClient.GetHostLabels(hostUUID)
	if err != nil {
		slog.Error("failed to get host labels", "hostUUID", hostUUID, "labelKey", labelKey, "error", err)
		return "", err
	}

	if res.Labels == nil {
		slog.Info("no labels found for host", "hostUUID", hostUUID)
		return "", nil
	}

	label, ok := res.Labels[labelKey]
	if !ok {
		slog.Info("label key not found", "hostUUID", hostUUID, "labelKey", labelKey)
		return "", nil
	}

	labelVal, ok := label.(string)
	if !ok {
		err := fmt.Errorf("host %s label %s has non-string value: %v", hostUUID, labelKey, label)
		slog.Error("invalid label value type", "error", err)
		return "", err
	}

	return config.HostLabel(labelVal), nil
}

func GetHostRole(clusterIP string, hostUUID string, roleInTower string) (string, error) {
	tunaClient := tuna.NewClient(clusterIP, config.TunaAPIToken)

	labelVal, err := GetHostLabelValue(tunaClient, hostUUID, config.LcmManagerHostRole)
	if err != nil {
		slog.Error("fail to get host labels", "host", hostUUID, "error", err)
		return "", err
	}

	slog.Info("get host role from label", "host", hostUUID, "role", labelVal)
	if string(labelVal) != "" {
		return string(labelVal), nil
	}

	slog.Info("no host role label, return role in tower db", "host", hostUUID, "role", roleInTower)
	return roleInTower, nil
}

func InitZbsClient(cluster *tower.QueryClusterInfoByLocalIdCluster) (*zbsclient.ZbsClient, error) {
	slog.Info("init storage api client", "cluster", cluster.Name)

	healthyMasterIPs := make([]string, 0)
	for _, host := range cluster.Hosts {
		if host.Role == string(config.HostRoleMaster) && host.Merged_status == tower.HostMergedStatusHealthy {
			healthyMasterIPs = append(healthyMasterIPs, host.Management_ip)
		}
	}

	if len(healthyMasterIPs) == 0 {
		return nil, errors.New("failed to create storage api client, no healthy master node")
	}

	var err error
	var zbsClient *zbsclient.ZbsClient

	sort.Strings(healthyMasterIPs)
	for _, hostIP := range healthyMasterIPs {
		zbsClient, err = zbsclient.NewZbsClient(hostIP)
		if err != nil {
			slog.Warn("failed to create storage api client", "host_ip", hostIP, "error", err)
			continue
		}

		// try to use zbs client
		_, err = zbsClient.IsExistPextendDead()
		if err != nil {
			slog.Warn("failed to connect to storage api", "host_ip", hostIP, "error", err)
			continue
		}

		slog.Info("finish init storage api client", "host_ip", hostIP)
		return zbsClient, nil
	}

	return nil, fmt.Errorf("failed to create storage api client in cluster %s: %w", cluster.Name, err)
}

type ActivityBaseHandler struct {
	jobRepository         postgres.IJobRepository
	checkResultRepository postgres.ICheckResultRepository
	towerClient           tower.Client
	hpClient              hpoperator.Client
	i18n                  *goeasyi18n.I18n
}

func NewActivityBaseHandler(
	jobRepository postgres.IJobRepository,
	checkResultRepository postgres.ICheckResultRepository,
	towerClient tower.Client,
	hpClient hpoperator.Client,
	i18n *goeasyi18n.I18n,
) *ActivityBaseHandler {
	return &ActivityBaseHandler{
		jobRepository:         jobRepository,
		checkResultRepository: checkResultRepository,
		towerClient:           towerClient,
		hpClient:              hpClient,
		i18n:                  i18n,
	}
}

func (h *ActivityBaseHandler) UpdateTowerTaskWithAuditLog(ctx context.Context, taskID string, taskInput *tower.UpdateTaskInput, header *CustomedHeader, auditInput *tower.AuditInput) error {
	slog.Info("UpdateTowerTaskWithAuditLog started.")

	taskHandler := tower.NewTaskHandler(h.towerClient, map[string]any{
		"user_id": header.UserID,
		"user_ip": header.UserIP,
	})

	taskHandler.SetTaskID(taskID)
	slog.Info("prepare tower audit log.", "audit_input", auditInput)
	taskHandler.PrepareAuditLog(auditInput)

	if err := taskHandler.UpdateTask(ctx, *taskInput); err != nil {
		slog.Error("UpdateTask failed.", "error", err)
		return err
	}

	return nil
}

func (h *ActivityBaseHandler) UpdateTowerTask(ctx context.Context, taskID string, taskInput *tower.UpdateTaskInput) error {
	slog.Info("UpdateTowerTask started.")

	taskHandler := tower.NewTaskHandler(h.towerClient, nil)
	taskHandler.SetTaskID(taskID)

	if err := taskHandler.UpdateTask(ctx, *taskInput); err != nil {
		slog.Error("UpdateTask failed.", "error", err)
		return err
	}

	return nil
}

func (h *ActivityBaseHandler) UpdateTowerTaskProgress(ctx context.Context, jobID string, taskID string, hostUUID string, args map[string]string, progress float64) error {
	slog.Info("update tower task progress", "task_id", taskID, "progress", progress)

	taskArgs := map[string]string{
		"job_id":    jobID,
		"host_uuid": hostUUID,
		"state":     "running",
	}

	if args != nil {
		taskArgs = args
	}

	taskInput := &tower.UpdateTaskInput{
		Progress: progress,
		Status:   tower.TaskStatusExecuting,
		Args:     taskArgs,
	}

	taskHandler := tower.NewTaskHandler(h.towerClient, nil)
	taskHandler.SetTaskID(taskID)

	if err := taskHandler.UpdateTask(ctx, *taskInput); err != nil {
		slog.Error("update tower task progress failed.", "error", err)
		return err
	}

	return nil
}

func (h *ActivityBaseHandler) trimCheckNamePrefix(name string) string {
	nameInfo := strings.Split(name, ".")

	if len(nameInfo) == 2 {
		return nameInfo[1]
	}

	return name
}

func (h *ActivityBaseHandler) UpdateCheckResult(cid string, jobID string, checkName string, state serverpb.CheckState, zhOptions *goeasyi18n.Options, enOptions *goeasyi18n.Options, isVmware bool) error {
	slog.Info("UpdateCheckResult", "JobID", jobID, "state", state.String())

	if zhOptions == nil {
		zhOptions = &goeasyi18n.Options{}
	}

	if enOptions == nil {
		enOptions = &goeasyi18n.Options{}
	}

	checkResult := &serverpb.CheckResult{
		Id:         cid,
		JobId:      jobID,
		CheckName:  h.trimCheckNamePrefix(checkName),
		CreateTime: timestamppb.New(time.Now()),
	}

	langZh, langEn := GetZhAndEnI18nLanguageName(isVmware)
	descKey := checkName + "Desc"
	checkResult.Description = []*serverpb.MessageItem{
		{
			Lang:    serverpb.MessageLang_LANG_EN,
			Message: h.i18n.T(langEn, descKey, *enOptions),
		},
		{
			Lang:    serverpb.MessageLang_LANG_ZH,
			Message: h.i18n.T(langZh, descKey, *zhOptions),
		},
	}

	var msgKey string
	switch state { //nolint: exhaustive
	case serverpb.CheckState_CHECK_STATE_WARNING:
		msgKey = checkName + "Warning"
	case serverpb.CheckState_CHECK_STATE_FAILED:
		msgKey = checkName + "Failed"
	case serverpb.CheckState_CHECK_STATE_SUCCESS:
		msgKey = checkName + "Success"
	}

	if msgKey != "" {
		zhMsg := h.i18n.T(langZh, msgKey, *zhOptions)
		enMsg := h.i18n.T(langEn, msgKey, *enOptions)

		if enMsg != "" || zhMsg != "" {
			checkResult.Messages = []*serverpb.MessageItem{
				{
					Lang:    serverpb.MessageLang_LANG_EN,
					Message: enMsg,
				},
				{
					Lang:    serverpb.MessageLang_LANG_ZH,
					Message: zhMsg,
				},
			}
		}
	}

	checkResult.State = state

	err := h.checkResultRepository.Save(context.Background(), checkResult)
	if err != nil {
		slog.Error("failed to save check result", "error", err)
		return err
	}

	return nil
}

func (h *ActivityBaseHandler) UpdateCheckResultWithoutMessage(cid string, jobID string, checkName string, state serverpb.CheckState, options *goeasyi18n.Options, isVmware bool) error {
	slog.Info("UpdateCheckResultWithoutMessage", "JobID", jobID, "state", state.String())

	langZh, langEn := GetZhAndEnI18nLanguageName(isVmware)

	if options == nil {
		options = &goeasyi18n.Options{}
	}

	checkResult := &serverpb.CheckResult{
		Id:         cid,
		JobId:      jobID,
		CheckName:  h.trimCheckNamePrefix(checkName),
		CreateTime: timestamppb.New(time.Now()),
	}

	descKey := checkName + "Desc"
	checkResult.Description = []*serverpb.MessageItem{
		{
			Lang:    serverpb.MessageLang_LANG_EN,
			Message: h.i18n.T(langEn, descKey, *options),
		},
		{
			Lang:    serverpb.MessageLang_LANG_ZH,
			Message: h.i18n.T(langZh, descKey, *options),
		},
	}

	checkResult.State = state

	err := h.checkResultRepository.Save(context.Background(), checkResult)
	if err != nil {
		slog.Error("failed to save check result", "error", err)
		return err
	}

	return nil
}

func (h *ActivityBaseHandler) BuildCreateTowerTaskInput(clusterID string, towerTaskCfgName string, zhOptions *goeasyi18n.Options, enOptions *goeasyi18n.Options, args map[string]string, isVmware bool) *tower.CreateTaskInput {
	langZh, langEn := GetZhAndEnI18nLanguageName(isVmware)

	if zhOptions == nil {
		zhOptions = &goeasyi18n.Options{}
	}

	if enOptions == nil {
		enOptions = &goeasyi18n.Options{}
	}

	towerTaskConfig := TowerTaskConfigs[towerTaskCfgName]

	var taskArgs interface{}
	if args != nil {
		taskArgs = args
	}

	return &tower.CreateTaskInput{
		Internal:  towerTaskConfig.Internal,
		ClusterID: clusterID,
		Description: tower.MessageI18n{
			"zh-CN": h.i18n.T(langZh, towerTaskConfig.TaskName, *zhOptions),
			"en-US": h.i18n.T(langEn, towerTaskConfig.TaskName, *enOptions),
		},
		Args:             taskArgs,
		Snapshot:         `{"typename":"Host"}`,
		ResourceType:     config.TowerTaskResourceType,
		ResourceMutation: towerTaskConfig.ResourceMutation,
	}
}

func (h *ActivityBaseHandler) CreateTowerTask(ctx context.Context, taskInput *tower.CreateTaskInput, header CustomedHeader) (string, error) {
	slog.Info("CreateTowerTask started.")

	taskHandler := tower.NewTaskHandler(h.towerClient, map[string]any{
		"user_id": header.UserID,
		"user_ip": header.UserIP,
	})

	if err := taskHandler.CreateTask(ctx, *taskInput); err != nil {
		slog.Error("CreateTask failed.", "error", err)

		return "", err
	}

	taskID := taskHandler.GetTaskID()

	return taskID, nil
}

func (h *ActivityBaseHandler) BuildTowerAuditEventInput(jobID string, clusterID string, towerTaskCfgName string, zhOptions *goeasyi18n.Options, enOptions *goeasyi18n.Options, isVmware bool) *tower.AuditInput {
	slog.Info("BuildTowerAuditEventInput started.")

	langZh, langEn := GetZhAndEnI18nLanguageName(isVmware)

	if zhOptions == nil {
		zhOptions = &goeasyi18n.Options{}
	}

	if enOptions == nil {
		enOptions = &goeasyi18n.Options{}
	}

	towerTaskConfig := TowerTaskConfigs[towerTaskCfgName]

	return &tower.AuditInput{
		Code: towerTaskConfig.AuditEventType,
		Message: tower.MessageI18n{
			"zh-CN": h.i18n.T(langZh, towerTaskConfig.AuditEventName, *zhOptions),
			"en-US": h.i18n.T(langEn, towerTaskConfig.AuditEventName, *enOptions),
		},
		ResourceID:   jobID,
		ResourceType: config.TowerTaskResourceType,
		ClusterID:    clusterID,
	}
}

func (h *ActivityBaseHandler) UpdateHostLabel(ctx context.Context, clusterUUID string, hostUUID string, labelKey string, labelVal config.HostLabel) error {
	slog.Info("UpdateHostLabel started.", "cluster", clusterUUID, "host", hostUUID, "label", labelKey, "value", labelVal)

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, clusterUUID)
	if err != nil {
		slog.Error("failed to get cluster info", "error", err)
		return err
	}

	healthyMasterIPs := make([]string, 0)
	for _, host := range cluster.Hosts {
		if host.Role == string(config.HostRoleMaster) && host.Merged_status == tower.HostMergedStatusHealthy {
			healthyMasterIPs = append(healthyMasterIPs, host.Management_ip)
		}
	}

	if len(healthyMasterIPs) == 0 {
		slog.Error("failed to update host label, no healthy master node", "cluster", clusterUUID)
		return errors.New("failed to update host label, no healthy master node")
	}

	sort.Strings(healthyMasterIPs)

	// try to use healthy master management ip to connect to tuna api one by one to improve robustness.
	for _, hostIP := range healthyMasterIPs {
		err = h.doUpdateHostLabel(hostIP, hostUUID, labelKey, labelVal)
		if err != nil {
			slog.Warn("failed to update host label by tuna api", "host_ip", hostIP, "error", err)
			continue
		}

		return nil
	}

	return fmt.Errorf("failed to update host label by tuna api in cluster %s: %w", clusterUUID, err)
}

func (h *ActivityBaseHandler) doUpdateHostLabel(hostIP string, hostUUID string, labelKey string, labelVal config.HostLabel) error {
	slog.Info("doUpdateHostLabel started.", "host_ip", hostIP, "host", hostUUID, "label", labelKey, "value", labelVal)

	// use tuna client to update host labels
	tunaClient := tuna.NewClient(hostIP, config.TunaAPIToken)

	res, err := tunaClient.GetHostLabels(hostUUID)
	if err != nil {
		slog.Error("failed to get host labels", "error", err)
		return err
	}

	labels := res.Labels
	slog.Info("host labels before update", "labels", labels)

	currentVal, exists := labels[labelKey]
	if (labelVal == "" && !exists) || (exists && currentVal == labelVal) {
		slog.Info("already meet the requirements, skip update host label")
		return nil
	}

	if labelVal == "" {
		delete(labels, labelKey)
	} else {
		labels[labelKey] = labelVal
	}

	res, err = tunaClient.UpdateHostLabels(hostUUID, labels, res.Version)
	if err != nil {
		slog.Error("failed to update host labels", "error", err)
		return err
	}

	slog.Info("host labels after update", "labels", res.Labels)

	return nil
}

func (h *ActivityBaseHandler) VerifyHostPluginInstalledAndReady(ctx context.Context, clusterUUID string, jobID string) (string, error) {
	slog.Info("VerifyHostPluginInstalledAndReady started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, clusterUUID)
	if err != nil {
		slog.Error("failed to get cluster info", "error", err)
		return "", err
	}

	pkgsYAML, err := hostplugin.NewPkgsYAML()
	if err != nil {
		slog.Error("failed to load host plugin package pkgs.yaml", "error", err)
		return "", err
	}

	pkg := pkgsYAML.GetPkgInfo(config.LcmManagerAgentPackageName, string(cluster.Architecture))
	if pkg == nil {
		slog.Error("failed to get host plugin package info.", "name", config.LcmManagerAgentPackageName, "Arch", cluster.Architecture)
		return "", fmt.Errorf("failed to get host plugin package %s, %s", config.LcmManagerAgentPackageName, cluster.Architecture)
	}

	res, err := h.hpClient.QuerySpecificHostPluginsByClusters(context.Background(), config.LcmHostPluginNamespace, []string{cluster.Id})
	if err != nil {
		slog.Error("failed to query host plugins.", "error", err)
		return "", err
	}

	for _, hp := range res {
		if hp.Host_plugin_package.Name != pkg.Name {
			continue
		}

		if pkg.IsPkgInstalled(hp) {
			slog.Info("target version host plugin was already installed, skip cleanup")

			// update job details with agent_addr
			if err := h.jobRepository.UpdateJobDetail(ctx, jobID, map[string]string{"agent_addr": hp.Hosts[0].Management_ip}); err != nil {
				slog.Error("fail to update job details with agent_addr")
				return "", err
			}

			return hp.Id, nil
		}

		if err := h.DeleteHostPlugin(hp.Id); err != nil {
			return "", err
		}
	}

	return "", nil
}

func (h *ActivityBaseHandler) CleanupHostPlugin(hostPluginID string) error {
	slog.Info("CleanupHostPlugin started.", "host_plugin_id", hostPluginID)

	hostPlugin, err := h.hpClient.GetHostPlugin(context.Background(), hostPluginID)
	if err != nil {
		slog.Error("fail to get host plugin.", "host_plugin_id", hostPluginID, "error", err)
		return err
	}

	if err := h.DeleteHostPlugin(hostPlugin.Id); err != nil {
		slog.Error("fail to delete host plugin.", "host_plugin_id", hostPluginID, "error", err)
		return err
	}

	slog.Info("CleanupHostPlugin finished", "hp_name", hostPlugin.Name)

	return nil
}

func (h *ActivityBaseHandler) DeleteHostPlugin(hostPluginID string) error {
	slog.Info("DeleteHostPlugin started.", "host_plugin_id", hostPluginID)

	_, taskID, err := h.hpClient.DeleteHostPlugin(context.Background(), hostPluginID, false)
	if err != nil {
		return fmt.Errorf("failed to delete host plugin %s: %w", hostPluginID, err)
	}

	towerTaskTimeout := 600
	if err := hostplugin.WaitTowerTask(h.towerClient, taskID, towerTaskTimeout); err == nil {
		return nil
	}

	_, taskID, err = h.hpClient.DeleteHostPlugin(context.Background(), hostPluginID, true)
	if err != nil {
		return fmt.Errorf("failed to force delete host plugin %s: %w", hostPluginID, err)
	}

	if err := hostplugin.WaitTowerTask(h.towerClient, taskID, towerTaskTimeout); err != nil {
		slog.Error(fmt.Sprintf("failed to force delete host plugin %s: %v", hostPluginID, err))
		return err
	}

	return nil
}

func (h *ActivityBaseHandler) PickHostToInstallHostPlugin(ctx context.Context, clusterUUID string, hostUUID string) (*tower.QueryClusterInfoByLocalIdClusterHostsHost, error) {
	slog.Info("PickHostToInstallHostPlugin started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, clusterUUID)
	if err != nil {
		slog.Error("failed to get cluster info", "error", err)
		return nil, err
	}

	sort.Slice(cluster.Hosts, func(i, j int) bool {
		return cluster.Hosts[i].Name < cluster.Hosts[j].Name
	})

	for _, host := range cluster.Hosts {
		if host.Local_id != hostUUID && host.Status == tower.HostStatusConnectedHealthy {
			slog.Info("Pick host to deploy lcm manager agent host plugin.", "host_name", host.Name, "mgt_ip", host.Management_ip)
			return &host, nil
		}
	}

	return nil, errors.New("fail to pick host to deploy lcm manager agent")
}

func (h *ActivityBaseHandler) UploadHostPluginPackage(ctx context.Context, clusterUUID string) (string, error) {
	slog.Info("UploadHostPluginPackage started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, clusterUUID)
	if err != nil {
		slog.Error("failed to get cluster info", "error", err)
		return "", err
	}

	pkgsYAML, err := hostplugin.NewPkgsYAML()
	if err != nil {
		slog.Error("failed to load host plugin package pkgs.yaml", "error", err)
		return "", err
	}

	pkg := pkgsYAML.GetPkgInfo(config.LcmManagerAgentPackageName, string(cluster.Architecture))
	if pkg == nil {
		slog.Error("failed to get host plugin package info.", "name", config.LcmManagerAgentPackageName, "Arch", cluster.Architecture)
		return "", fmt.Errorf("failed to get host plugin package %s, %s", config.LcmManagerAgentPackageName, cluster.Architecture)
	}

	slog.Info("get local package info.", "pkg", pkg)

	queryRes, err := h.hpClient.QueryHostPluginPackage(context.Background(), pkg.Name, pkg.Version, tower.Architecture(pkg.Architecture))
	if err != nil {
		slog.Error("failed to query host plugin package from tower.", "name", pkg.Name, "version", pkg.Version, "error", err)
		return "", err
	}

	slog.Info("get host plugin package from tower", "pkg_info", queryRes)

	pkgID := ""

	if len(queryRes) == 0 {
		slog.Info("do upload host plugin package", "pkg", pkg.Name)

		id, err := pkg.Upload()
		if err != nil {
			return "", err
		}

		pkgID = id
	} else {
		slog.Info("skip upload host plugin package", "pkg", pkg.Name)

		pkgID = queryRes[0].Id
	}

	slog.Info("lcm manager agent hostplugin pkg id", "pkg_id", pkgID)

	return pkgID, nil
}

func (h *ActivityBaseHandler) PrepareInstallHostPluginInput(clusterID string, pkgID string, targetHostID string) *schema.HostPluginCreateInput {
	slog.Info("PrepareInstallHostPluginInput started.")

	hpName := fmt.Sprintf("%s-%s", config.LcmManagerAgentPackageName, clusterID)
	hpinput := &schema.HostPluginCreateInput{
		Name:      hpName,
		Namespace: config.LcmHostPluginNamespace,
		HostPluginPackage: &schema.HostPluginPackageInput{
			Connect: &schema.Connect{
				ID: pkgID,
			},
		},
		Cluster: &schema.ClusterInput{
			Connect: &schema.Connect{
				ID: clusterID,
			},
		},
		Hosts: &schema.HostInput{
			Connect: []*schema.Connect{
				{
					ID: targetHostID,
				},
			},
		},
		DisableSelector: true,
		Values: &schema.Values{
			Set: []string{},
		},
	}

	slog.Info("generate host plugin creation input.", "input", hpinput)

	return hpinput
}

func (h *ActivityBaseHandler) InstallHostPlugin(ctx context.Context, targetHostIP string, jobID string, hpInput *schema.HostPluginCreateInput) (string, error) {
	slog.Info("InstallHostPlugin started.")

	createHpRes, taskID, err := h.hpClient.CreateHostPlugin(context.Background(), hpInput, nil, 10) //nolint:gomnd
	if err != nil {
		slog.Error("failed to create host plugin.", "error", err)
		return "", err
	}

	if createHpRes.Id == "" {
		slog.Error("failed to create host plugin, host plugin id not in response")
		return "", errors.New("failed to create host plugin, host plugin id not in response")
	}

	towerTaskTimeout := 600
	if err := hostplugin.WaitTowerTask(h.towerClient, taskID, towerTaskTimeout); err != nil {
		slog.Error("failed to create host plugin.", "taskID", taskID, "error", err)
		return "", err
	}

	res, err := h.hpClient.GetHostPlugin(context.Background(), createHpRes.Id)
	if err != nil {
		slog.Error("fail to get new created host plugin.", "host_plugin_id", createHpRes.Id, "error", err)
		return "", err
	}

	hpInstances := hostplugin.UnmarshalInstances(res.Host_plugin_instances)
	if hpInstances == nil {
		slog.Error("fail to get new created host plugin instances", "host_plugin_id", createHpRes.Id)
		return "", fmt.Errorf("fail to get hostplugin instance of %s", hpInput.Name)
	}

	// update job details with agent_addr
	if err := h.jobRepository.UpdateJobDetail(ctx, jobID, map[string]string{"agent_addr": targetHostIP}); err != nil {
		slog.Error("fail to update job details with agent_addr")
		return "", err
	}

	return createHpRes.Id, nil
}

func (h *ActivityBaseHandler) ReadAvailabilityMapFile() (*config.AvailabilityMap, error) {
	slog.Info("ReadAvailabilityMapFile started.", "file_name", config.AvailabilityMapFile)

	fileData, err := os.ReadFile(config.AvailabilityMapFile)
	if err != nil {
		slog.Error("fail to read yaml file.", "file", config.AvailabilityMapFile, "error", err)
		return nil, err
	}

	var availabilityMap config.AvailabilityMap

	err = yaml.Unmarshal(fileData, &availabilityMap)
	if err != nil {
		slog.Error("fail to unmarshal yaml", "error", err)
		return nil, err
	}

	return &availabilityMap, nil
}

// LoadTargetActionData availabilities are load from availability map file and filtered by product and version and action name
func (h *ActivityBaseHandler) LoadTargetActionData(ctx context.Context, clusterUUID string, targetActionName string) (*config.VersionedAction, error) {
	slog.Info("LoadTargetActionData started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, clusterUUID)
	if err != nil {
		slog.Error("failed to get cluster info", "error", err)
		return nil, err
	}

	var productName string

	switch cluster.Type { //nolint: exhaustive
	case tower.ClusterTypeSmtxElf:
		productName = "SMTXELF"
	case tower.ClusterTypeSmtxOs:
		productName = "SMTXOS"
	case tower.ClusterTypeSmtxZbs:
		productName = "SMTXZBS"
	default:
		return nil, fmt.Errorf("unsupported product %s", cluster.Type)
	}

	availabilityMap, err := h.ReadAvailabilityMapFile()
	if err != nil {
		return nil, err
	}

	var supportedActions []config.Action

	for _, productAction := range availabilityMap.ProductActions {
		if productAction.ProductName == productName {
			slog.Info("get product supported actions success", "product", productName)

			supportedActions = productAction.Actions

			break
		}
	}

	var versionedActions []config.VersionedAction

	for _, supportedAction := range supportedActions {
		if supportedAction.Name == targetActionName {
			slog.Info("get product supported versioned actions success", "action", targetActionName)

			versionedActions = supportedAction.VersionedActions

			break
		}
	}

	clusterVersion, err := version.NewVersion(cluster.Version)
	if err != nil {
		return nil, fmt.Errorf("fail to parse cluster version %s, err: %s", cluster.Version, err.Error())
	}

	for _, versionedAction := range versionedActions {
		for _, v := range versionedAction.Versions {
			constraint, err := version.NewConstraint(v)
			if err != nil {
				return nil, fmt.Errorf("fail to parse version constraint %s, err: %s", v, err.Error())
			}

			if constraint.Check(clusterVersion) {
				slog.Info("get target action data success", "action_data", versionedAction)
				return &versionedAction, nil
			}
		}
	}

	return nil, fmt.Errorf("can't get %s commands of product %s, version %s", targetActionName, productName, cluster.Version)
}

func (h *ActivityBaseHandler) GetAgentCmdByName(agentCmds []config.AgentCmd, name config.CmdName) (*config.AgentCmd, error) {
	for _, cmd := range agentCmds {
		if cmd.Name == name {
			return &cmd, nil
		}
	}

	return nil, fmt.Errorf("command %s not found", name)
}

func (h *ActivityBaseHandler) StoreJobLogs(ctx context.Context, jobID string, agentTaskIDs []string) error {
	slog.Info("StoreJobLogs started.", "job_id", jobID)

	agentAddr, err := h.GetJobLcmAgentAddrFromDB(ctx, jobID)
	if err != nil {
		return err
	}

	logFilePath := config.LogDir + "/" + jobID + ".log"

	file, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_TRUNC|os.O_WRONLY, 0o644)
	if err != nil {
		return fmt.Errorf("failed to open or create file: %w", err)
	}

	defer file.Close()

	agentClient := agentclient.NewTaskManagerClient(agentAddr)

	for _, taskID := range agentTaskIDs {
		slog.Info("get task log", "agent_task_id", taskID)

		taskResult, err := agentClient.GetTask(ctx, &agentpb.GetTaskRequest{TaskId: taskID})
		if err != nil {
			slog.Error("Fail to get lcm manager agent task", "agent_task_id", taskID, "error", err)
			return err
		}

		_, err = file.WriteString(taskResult.Data.Output.Stdout + "\n")
		if err != nil {
			return fmt.Errorf("failed to write to file: %w", err)
		}
	}

	slog.Info("StoreJobLogs done.", "job_id", jobID)

	return nil
}

func (h *ActivityBaseHandler) GetJobLcmAgentAddrFromDB(ctx context.Context, jobID string) (string, error) {
	slog.Info("GetJobLcmAgentAddrFromDB")

	job, err := h.jobRepository.Get(ctx, jobID)
	if err != nil {
		slog.Error("failed to get job from db", "job_id", jobID)
		return "", err
	}

	agentAddr, exist := job.Details["agent_addr"]
	if !exist {
		slog.Error("agent_addr not found in job")
		return "", errors.New("agent_addr not found in job")
	}

	return agentAddr, nil
}

func (h *ActivityBaseHandler) GetTowerTaskIDFromDB(ctx context.Context, jobID string) (string, error) {
	slog.Info("GetTowerTaskIDFromDB")

	job, err := h.jobRepository.Get(ctx, jobID)
	if err != nil {
		slog.Error("failed to get job from db", "job_id", jobID)
		return "", err
	}

	taskID, exist := job.Details["tower_task_id"]
	if !exist {
		slog.Error("failed to get tower_task_id from job", "job_id", job.Id)
		return "", errors.New("tower_task_id not found in job")
	}

	return taskID, nil
}

func (h *ActivityBaseHandler) CreateLcmAgentTask(ctx context.Context, jobID string, agentInput *agentpb.TaskInput) (string, error) {
	slog.Info("CreateLcmAgentTask started.", "input", agentInput)

	agentAddr, err := h.GetJobLcmAgentAddrFromDB(ctx, jobID)
	if err != nil {
		return "", err
	}

	agentClient := agentclient.NewTaskManagerClient(agentAddr)

	agentTask, err := agentClient.CreateTask(ctx, agentInput)
	if err != nil {
		slog.Error("Fail to create lcm manager agent task.", "error", err)
		return "", err
	}

	return agentTask.Data.TaskId, nil
}

func (h *ActivityBaseHandler) WaitingForActionDone(ctx context.Context, jobID string, agentInput *agentpb.TaskInput, taskID string) error {
	slog.Info("WaitingForActionDone started.")

	agentAddr, err := h.GetJobLcmAgentAddrFromDB(ctx, jobID)
	if err != nil {
		return err
	}

	timeout := time.After(time.Duration(agentInput.Timeout) * time.Second)

	agentClient := agentclient.NewTaskManagerClient(agentAddr)
	taskInput := &agentpb.GetTaskRequest{TaskId: taskID}

	for {
		select {
		case <-timeout:
			slog.Error("Timeout reached waiting for task completion")
			return fmt.Errorf("timeout waiting for task %s completion", taskID)

		default:
			time.Sleep(30 * time.Second)

			done, err := h.CheckAgentTaskStatus(ctx, agentClient, taskInput)
			if err != nil {
				return err
			}

			if done {
				return nil
			}
		}
	}
}

func (h *ActivityBaseHandler) CheckAgentTaskStatus(ctx context.Context, client agentclient.TaskManagerClient, input *agentpb.GetTaskRequest) (bool, error) {
	taskResult, err := client.GetTask(ctx, input)
	if err != nil {
		slog.Error("Fail to get lcm manager agent task", "task_id", input.TaskId, "error", err)
		return false, err
	}

	switch taskResult.Data.Status.Status {
	case agentpb.TaskStatusEnum_COMPLETED:
		slog.Info("lcm manager agent task finished with success")
		return true, nil
	case agentpb.TaskStatusEnum_RUNNING:
		slog.Info("lcm manager agent task still running, wait")
		return false, nil
	case agentpb.TaskStatusEnum_FAILED, agentpb.TaskStatusEnum_PENDING, agentpb.TaskStatusEnum_UNSPECIFIED:
		slog.Error("lcm manager agent task status not right", "task_status", taskResult.Data.Status.Status.String())
		return false, temporal.NewNonRetryableApplicationError("task status not right", "AgentTaskFailed", nil, nil)
	}

	return false, nil
}

func (h *ActivityBaseHandler) CheckHostServiceStatus(ctx context.Context, tunaClient *tuna.Client, services []string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("check host service status.", "services", services)

	servicesStatus, err := tunaClient.GetServices()
	if err != nil {
		msg := "fail to get host service status, error: " + err.Error()
		logger.Error(msg)
		return errors.New(msg)
	}

	targetServicesStatus := make(map[string]*tuna.HostServiceInfo)
	for _, serviceStatus := range servicesStatus {
		if slices.Contains(services, serviceStatus.ServiceName) {
			logger.Info("get target service status", "service", serviceStatus.ServiceName, "service_state", serviceStatus.RunningState)
			targetServicesStatus[serviceStatus.ServiceName] = serviceStatus
		}
	}

	for _, service := range services {
		if _, ok := targetServicesStatus[service]; !ok {
			msg := fmt.Sprintf("check host service status failed, service: %s, service_state: %s", service, "not found")
			logger.Error(msg)
			return errors.New(msg)
		}

		if targetServicesStatus[service].RunningState != "active" {
			msg := fmt.Sprintf("check host service status failed, service: %s, service_state: %s", service, targetServicesStatus[service].RunningState)
			logger.Error(msg)
			return errors.New(msg)
		}
	}

	return nil
}
