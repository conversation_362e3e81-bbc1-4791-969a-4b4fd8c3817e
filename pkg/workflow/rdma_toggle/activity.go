package rdmatoggle

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/eduardolat/goeasyi18n"
	"go.temporal.io/sdk/activity"

	agentpb "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	serverpb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	agentclient "github.smartx.com/LCM/lcm-manager/pkg/client/agent"
	"github.smartx.com/LCM/lcm-manager/pkg/server/postgres"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
	hpoperator "github.smartx.com/LCM/lcm-manager/third_party/host_plugin_operator"
	tower "github.smartx.com/LCM/lcm-manager/third_party/tower"
)

type RdmaToggleActivities struct {
	baseHandler           *workflowutils.ActivityBaseHandler
	jobRepository         postgres.IJobRepository
	checkResultRepository postgres.ICheckResultRepository
	towerClient           tower.Client
	i18n                  *goeasyi18n.I18n
}

func NewRdmaToggleActivities(
	jobRepository postgres.IJobRepository,
	checkResultRepository postgres.ICheckResultRepository,
	towerClient tower.Client,
	hpClient hpoperator.Client,
	i18n *goeasyi18n.I18n,
) *RdmaToggleActivities {
	baseHandler := workflowutils.NewActivityBaseHandler(jobRepository, checkResultRepository, towerClient, hpClient, i18n)

	return &RdmaToggleActivities{
		baseHandler:           baseHandler,
		jobRepository:         jobRepository,
		checkResultRepository: checkResultRepository,
		towerClient:           towerClient,
		i18n:                  i18n,
	}
}

// --- job control ---
func (h *RdmaToggleActivities) RdmaToggleUpdateJobStateWithRunning(ctx context.Context, input RdmaToggleWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_RUNNING, nil)
}

func (h *RdmaToggleActivities) RdmaToggleUpdateJobStateWithSuccess(ctx context.Context, input RdmaToggleWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_SUCCESS, nil)
}

func (h *RdmaToggleActivities) RdmaToggleUpdateJobStateWithFailed(ctx context.Context, input RdmaToggleWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_FAILED, nil)
}

// --- tower job control ----
func (h *RdmaToggleActivities) RdmaToggleCreateTowerTask(ctx context.Context, input RdmaToggleWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleCreateTowerTask started.")

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"ClusterName": input.ClusterName,
		},
	}

	taskArgs := map[string]string{
		"job_id":    input.JobID,
		"host_uuid": input.HostUUID,
		"state":     "running",
	}

	taskInput := h.baseHandler.BuildCreateTowerTaskInput(input.TowerClusterID, input.TowerTaskCfgName, options, options, taskArgs, input.IsVmware)

	taskID, err := h.baseHandler.CreateTowerTask(ctx, taskInput, input.Header)
	if err != nil {
		return "", err
	}

	if err := h.jobRepository.UpdateJobDetail(ctx, input.JobID, map[string]string{"tower_task_id": taskID}); err != nil {
		logger.Error("fail to update job details with tower_task_id")
		return "", err
	}

	return taskID, nil
}

func (h *RdmaToggleActivities) RdmaToggleUpdateTowerTaskWithSuccess(ctx context.Context, input RdmaToggleWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggle UpdateTowerTaskWithSuccess started.")

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"ClusterName": input.ClusterName,
		},
	}

	taskInput := &tower.UpdateTaskInput{
		Progress: 1.0,
		Status:   tower.TaskStatusSuccessed,
		Args: map[string]string{
			"job_id":    input.JobID,
			"host_uuid": input.HostUUID,
			"state":     "finished",
		},
		Done: true,
	}

	auditInput := h.baseHandler.BuildTowerAuditEventInput(input.JobID, input.TowerClusterID, input.TowerTaskCfgName, options, options, input.IsVmware)

	if err := h.baseHandler.UpdateTowerTaskWithAuditLog(ctx, taskID, taskInput, &input.Header, auditInput); err != nil {
		logger.Error("UpdateTowerTaskWithAuditLog failed.", err)
		return err
	}

	return nil
}

func (h *RdmaToggleActivities) RdmaToggleUpdateTowerTaskWithFailed(ctx context.Context, input RdmaToggleWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleUpdateTowerTaskWithFailed started.")

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"ClusterName": input.ClusterName,
		},
	}

	taskInput := &tower.UpdateTaskInput{
		Status: tower.TaskStatusFailed,
		Args: map[string]string{
			"job_id":    input.JobID,
			"host_uuid": input.HostUUID,
			"state":     "failed",
		},
		Done: true,
	}

	auditInput := h.baseHandler.BuildTowerAuditEventInput(input.JobID, input.TowerClusterID, input.TowerTaskCfgName, options, options, input.IsVmware)

	if err := h.baseHandler.UpdateTowerTaskWithAuditLog(ctx, taskID, taskInput, &input.Header, auditInput); err != nil {
		logger.Error("RdmaToggleUpdateTowerTaskWithAuditLog failed.", err)
		return err
	}

	return nil
}

func (h *RdmaToggleActivities) RdmaToggleStoreJobLogs(ctx context.Context, input RdmaToggleWorkflowInput, agentTaskIDs []string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleStoreJobLogs started.", input)

	return h.baseHandler.StoreJobLogs(ctx, input.JobID, agentTaskIDs)
}

func (h *RdmaToggleActivities) RdmaToggleCleanupHostPlugin(_ context.Context, hostPluginID string) error {
	return h.baseHandler.CleanupHostPlugin(hostPluginID)
}

func (h *RdmaToggleActivities) RdmaToggleVerifyHostPluginInstalledAndReady(ctx context.Context, input RdmaToggleWorkflowInput) (string, error) {
	return h.baseHandler.VerifyHostPluginInstalledAndReady(ctx, input.ClusterUUID, input.JobID)
}

func (h *RdmaToggleActivities) RdmaToggleInstallHostPlugin(ctx context.Context, input RdmaToggleWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleInstallHostPlugin started.")

	host, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		return "", err
	}

	pkgID, err := h.baseHandler.UploadHostPluginPackage(ctx, input.ClusterUUID)
	if err != nil {
		return "", err
	}

	hpInput := h.baseHandler.PrepareInstallHostPluginInput(input.TowerClusterID, pkgID, host.Id)

	return h.baseHandler.InstallHostPlugin(ctx, host.Management_ip, input.JobID, hpInput)
}

// Step 1: Pre-check all nodes
func (h *RdmaToggleActivities) RdmaToggleGeneratePreCheckAgentInputs(ctx context.Context, input RdmaToggleWorkflowInput) ([]*agentpb.TaskInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleGeneratePreCheckAgentInputs started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("fail to get cluster info", "error", err)
		return nil, err
	}

	var agentInputs []*agentpb.TaskInput
	for _, host := range cluster.Hosts {
		agentInput := &agentpb.TaskInput{
			Command:  "echo test precheck",
			TargetIp: host.Management_ip,
			CmdQa:    nil,
			Timeout:  60,
		}
		agentInputs = append(agentInputs, agentInput)
	}

	logger.Info("generate rdma toggle precheck agent cmd inputs", "count", len(agentInputs))
	return agentInputs, nil
}

// Step 2: Configure all nodes
func (h *RdmaToggleActivities) RdmaToggleGenerateConfigAgentInputs(ctx context.Context, input RdmaToggleWorkflowInput) ([]*agentpb.TaskInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleGenerateConfigAgentInputs started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("fail to get cluster info", "error", err)
		return nil, err
	}

	var agentInputs []*agentpb.TaskInput
	for _, host := range cluster.Hosts {
		agentInput := &agentpb.TaskInput{
			Command:  "echo test config",
			TargetIp: host.Management_ip,
			CmdQa:    nil,
			Timeout:  120,
		}
		agentInputs = append(agentInputs, agentInput)
	}

	logger.Info("generate rdma toggle config agent cmd inputs", "count", len(agentInputs))
	return agentInputs, nil
}

// Step 3: Restart all nodes
func (h *RdmaToggleActivities) RdmaToggleGenerateRestartAgentInputs(ctx context.Context, input RdmaToggleWorkflowInput) ([]*agentpb.TaskInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleGenerateRestartAgentInputs started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("fail to get cluster info", "error", err)
		return nil, err
	}

	var agentInputs []*agentpb.TaskInput
	for _, host := range cluster.Hosts {
		agentInput := &agentpb.TaskInput{
			Command:  "echo test restart",
			TargetIp: host.Management_ip,
			CmdQa:    nil,
			Timeout:  300,
		}
		agentInputs = append(agentInputs, agentInput)
	}

	logger.Info("generate rdma toggle restart agent cmd inputs", "count", len(agentInputs))
	return agentInputs, nil
}

func (h *RdmaToggleActivities) RdmaToggleTriggerAgentTasks(ctx context.Context, input RdmaToggleWorkflowInput, agentInputs []*agentpb.TaskInput) ([]string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggle TriggerAgentTasks started.")

	var taskIDs []string
	for _, agentInput := range agentInputs {
		taskID, err := h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, agentInput)
		if err != nil {
			return nil, err
		}
		taskIDs = append(taskIDs, taskID)
	}

	return taskIDs, nil
}

// RdmaToggleWaitingForAgentTasks wait for all agent tasks done and update progress if not timeout
func (h *RdmaToggleActivities) RdmaToggleWaitingForAgentTasks(ctx context.Context, input RdmaToggleWorkflowInput, agentInputs []*agentpb.TaskInput, taskIDs []string, progressEvaluateInput *RdmaToggleProgressEvaluateInput, stepName string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleWaitingForAgentTasks started.", "step", stepName)

	towerTaskID, err := h.baseHandler.GetTowerTaskIDFromDB(ctx, input.JobID)
	if err != nil {
		logger.Error("failed to get tower task id in db", "job_id", input.JobID)
		return err
	}

	agentAddr, err := h.baseHandler.GetJobLcmAgentAddrFromDB(ctx, input.JobID)
	if err != nil {
		return err
	}

	// Find max timeout among all agent inputs
	maxTimeout := int32(0)
	for _, agentInput := range agentInputs {
		if agentInput.Timeout > maxTimeout {
			maxTimeout = agentInput.Timeout
		}
	}

	timeout := time.After(time.Duration(maxTimeout) * time.Second)
	agentClient := agentclient.NewTaskManagerClient(agentAddr)

	for {
		select {
		case <-timeout:
			slog.Error("Timeout reached waiting for tasks completion", "step", stepName)
			return fmt.Errorf("timeout waiting for %s tasks completion", stepName)

		default:
			time.Sleep(30 * time.Second)

			allDone := true
			for _, taskID := range taskIDs {
				taskInput := &agentpb.GetTaskRequest{TaskId: taskID}
				done, err := h.baseHandler.CheckAgentTaskStatus(ctx, agentClient, taskInput)
				if err != nil {
					return err
				}
				if !done {
					allDone = false
					break
				}
			}

			if allDone {
				return nil
			}

			if err := h.metaRdmaToggleUpdateProgress(ctx, input.JobID, input.HostUUID, towerTaskID, progressEvaluateInput, stepName); err != nil {
				logger.Error("fail to update rdma toggle progress, skip update it this time.", "error", err)
			}
		}
	}
}

type RdmaToggleProgressEvaluateInput struct {
	StartTime             time.Time
	EstimatedPreCheckTime int
	EstimatedConfigTime   int
	EstimatedRestartTime  int
	CurrentStep           string
	NodeIp                string
	CurrentStepStartTime  time.Time
}

// func (h *RdmaToggleActivities) RdmaToggleCostTimePreliminaryEvaluate(ctx context.Context, input RdmaToggleWorkflowInput) (*RdmaToggleProgressEvaluateInput, error) {
// 	logger := activity.GetLogger(ctx)
// 	logger.Info("RdmaToggle CostTimePreliminaryEvaluate started.")

// 	startTime, err := h.getRdmaToggleJobStartTime(ctx, input)
// 	if err != nil {
// 		logger.Error("fail to get job start time", "job_id", input.JobID)
// 		return nil, err
// 	}

// 	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
// 	if err != nil {
// 		logger.Error("fail to get cluster info", "error", err)
// 		return nil, err
// 	}

// 	nodeCount := len(cluster.Hosts)

// 	// Estimate time based on node count
// 	estimatedPreCheckTime := 60 * nodeCount // 1 minute per node
// 	estimatedConfigTime := 120 * nodeCount  // 2 minutes per node
// 	estimatedRestartTime := 300 * nodeCount // 5 minutes per node

// 	logger.Info("RdmaToggle CostTimePreliminaryEvaluate finished", "rdmaToggleProgressEvaluateInput", rdmaToggleProgressEvaluateInput)

// 	return rdmaToggleProgressEvaluateInput, nil
// }

func (h *RdmaToggleActivities) metaRdmaToggleUpdateProgress(ctx context.Context, jobID string, hostUUID string, towerTaskID string, progressEvaluateInput *RdmaToggleProgressEvaluateInput, stepName string) error {

	logger := activity.GetLogger(ctx)
	logger.Info("meta RdmaToggle UpdateProgress started.", "step", stepName)

	currentTime := time.Now().UTC()

	// Define task status map
	type TaskStatus struct {
		IsCompleted bool
		Weight      float64
		StartTime   time.Time
		Duration    int
	}

	taskProgressMap := make(map[string]map[string]TaskStatus)

	steps := []struct {
		name     string
		weight   float64
		duration int
		start    time.Time
	}{
		{
			name:     "precheck",
			weight:   0.2,
			duration: progressEvaluateInput.EstimatedPreCheckTime,
			start:    progressEvaluateInput.StartTime,
		},
		{
			name:     "config",
			weight:   0.3,
			duration: progressEvaluateInput.EstimatedConfigTime,
			start:    time.Time{},
		},
		{
			name:     "restart",
			weight:   0.5,
			duration: progressEvaluateInput.EstimatedRestartTime,
			start:    time.Time{},
		},
	}

	for _, step := range steps {
		ipStatusMap := make(map[string]TaskStatus)
		for _, ip := range ipList {
			ipStatusMap[ip] = TaskStatus{
				IsCompleted: false,
				Weight:      step.weight,
				StartTime:   step.start,
				Duration:    step.duration,
			}
		}
		taskProgressMap[step.name] = ipStatusMap
	}

	// Calculate progress based on current step
	var progress float64
	for step, status := range taskStatus {
		if step == stepName {
			// Current step - calculate partial progress
			stepUsedSec := int(currentTime.Sub(progressEvaluateInput.CurrentStepStartTime).Seconds())
			stepProgress := float64(stepUsedSec) / float64(status.duration)
			if stepProgress > 1.0 {
				stepProgress = 1.0
			}
			progress += stepProgress * status.weight
		} else if step < stepName {
			// Completed steps - add full weight
			progress += status.weight
		}
		// Future steps - add nothing
	}

	// Ensure progress is between 0 and 1
	if progress > 1.0 {
		progress = 1.0
	}
	if progress < 0.0 {
		progress = 0.0
	}

	// Update Tower task progress
	taskInput := &tower.UpdateTaskInput{
		Progress: progress,
		Args: map[string]string{
			"job_id":       jobID,
			"host_uuid":    hostUUID,
			"state":        "running",
			"current_step": stepName,
		},
	}

	if err := h.baseHandler.UpdateTowerTask(ctx, towerTaskID, taskInput); err != nil {
		logger.Error("failed to update tower task progress", "error", err, "progress", progress, "step", stepName)
		return err
	}

	logger.Info("successfully updated tower task progress", "progress", progress, "step", stepName)
	return nil
}
