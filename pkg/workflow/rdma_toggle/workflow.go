package rdmatoggle

import (
	"errors"

	"go.temporal.io/sdk/workflow"

	agentpb "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	server_pb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
)

type RdmaToggleWorkflowInput struct {
	Header           workflowutils.CustomedHeader `json:"header"`
	JobID            string                       `json:"job_id"`
	TowerClusterID   string                       `json:"tower_cluster_id"`
	ClusterUUID      string                       `json:"cluster_uuid"`
	ClusterName      string                       `json:"cluster_name"`
	ClusterIP        string                       `json:"cluster_vip"`
	HostUUID         string                       `json:"host_uuid"`
	HostName         string                       `json:"host_name"`
	IsVmware         bool                         `json:"is_vmware"`
	TowerTaskCfgName string                       `json:"tower_task_cfg_name"`
	RdmaEnabled      bool                         `json:"rdma_enabled"` // true to enable RDMA, false to disable
	TargetState      server_pb.RDMAState          `json:"target_state"`
}

func RdmaToggleWorkflow(ctx workflow.Context, input RdmaToggleWorkflowInput) error { //nolint: funlen
	var err error

	ctx, err = workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameDefault)
	if err != nil {
		return err
	}

	ctxStateUpdate, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameStateUpdate)
	if err != nil {
		return err
	}

	ctxActionWait, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameWaitingForRDMAToggle)
	if err != nil {
		return err
	}

	logger := workflow.GetLogger(ctx)
	logger.Info("RdmaToggleWorkflow workflow started", "JobID", input.JobID)

	var (
		h                               *RdmaToggleActivities
		realActionStarted               bool
		towerTaskID                     string
		hostPluginID                    string
		rdmaToggleProgressEvaluateInput *RdmaToggleProgressEvaluateInput
	)
	rdmaToggleProgressEvaluateInput = &RdmaToggleProgressEvaluateInput{
		CurrentStep: "init",
	}

	// lcm-manager-agent task ids, used for collecting logs from lcm-manager-agent
	agentTaskIDs := make([]string, 0)

	defer func() {
		if errors.Is(ctx.Err(), workflow.ErrCanceled) {
			// When the Workflow is canceled, it has to get a new disconnected context to execute any Activities
			ctx, _ = workflow.NewDisconnectedContext(ctx)
		}

		// store job logs
		if realActionStarted {
			if e := workflow.ExecuteActivity(ctx, h.RdmaToggleStoreJobLogs, input, agentTaskIDs).Get(ctx, nil); e != nil {
				logger.Error("fail to store lcm manager agent logs", "err", e)
			}
		}

		// host plugin: clean
		if hostPluginID != "" {
			if e := workflow.ExecuteActivity(ctx, h.RdmaToggleCleanupHostPlugin, hostPluginID).Get(ctx, nil); e != nil {
				logger.Error("fail to cleanup host plugin", "err", e)
			}
		}

		// update job state: failed
		if err != nil {
			if e := workflow.ExecuteActivity(ctxStateUpdate, h.RdmaToggleUpdateJobStateWithFailed, input).Get(ctx, nil); e != nil {
				logger.Error("fail to update rdma toggle job state with failed", "err", e)
			}

			if towerTaskID != "" {
				if e := workflow.ExecuteActivity(ctxStateUpdate, h.RdmaToggleUpdateTowerTaskWithFailed, input, towerTaskID).Get(ctx, nil); e != nil {
					logger.Error("fail to update rdma toggle tower task")
				}
			}
		}
	}()

	// 1. update job state: running
	if err = workflow.ExecuteActivity(ctxStateUpdate, h.RdmaToggleUpdateJobStateWithRunning, input).Get(ctx, nil); err != nil {
		logger.Error("fail to update job state")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.RdmaToggleCreateTowerTask, input).Get(ctx, &towerTaskID); err != nil {
		logger.Error("fail to create rdma toggle tower task")
		return err
	}

	// 2. host plugin: install
	if err = workflow.ExecuteActivity(ctx, h.RdmaToggleVerifyHostPluginInstalledAndReady, input).Get(ctx, &hostPluginID); err != nil {
		logger.Error("fail to verify host plugin installed and ready")
		return err
	}

	if hostPluginID == "" {
		if err = workflow.ExecuteActivity(ctx, h.RdmaToggleInstallHostPlugin, input).Get(ctx, &hostPluginID); err != nil {
			logger.Error("fail to install host plugin")
			return err
		}
	}

	// 3. Step 1: Pre-check all nodes
	logger.Info("Starting RDMA toggle pre-check phase")
	var preCheckAgentTaskInputs []*agentpb.TaskInput
	if err = workflow.ExecuteActivity(ctx, h.RdmaToggleGeneratePreCheckAgentInputs, input).Get(ctx, &preCheckAgentTaskInputs); err != nil {
		logger.Error("fail to generate pre-check agent inputs")
		return err
	}

	if preCheckAgentTaskInputs == nil || len(preCheckAgentTaskInputs) == 0 {
		err = errors.New("fail to generate pre-check agent inputs")
		return err
	}

	var preCheckTaskIDs []string
	if err = workflow.ExecuteActivity(ctx, h.RdmaToggleTriggerAgentTasks, input, preCheckAgentTaskInputs).Get(ctx, &preCheckTaskIDs); err != nil {
		logger.Error("fail to trigger pre-check agent tasks", "err", err)
		return err
	}
	agentTaskIDs = append(agentTaskIDs, preCheckTaskIDs...)
	realActionStarted = true

	if err = workflow.ExecuteActivity(ctxActionWait, h.RdmaToggleWaitingForAgentTasks, input, preCheckAgentTaskInputs, preCheckTaskIDs, rdmaToggleProgressEvaluateInput, "precheck").Get(ctx, nil); err != nil {
		logger.Error("fail to wait for pre-check agent tasks", "err", err)
		return err
	}

	// Update progress step to precheck
	rdmaToggleProgressEvaluateInput.CurrentStep = "precheck"
	rdmaToggleProgressEvaluateInput.CurrentStepStartTime = workflow.Now(ctx)

	for i, agentInput := range preCheckAgentTaskInputs {
		var taskIDs []string
		if err := workflow.ExecuteActivity(ctx, h.RdmaToggleTriggerAgentTasks, input, []*agentpb.TaskInput{agentInput}).Get(ctx, &taskIDs); err != nil {
			logger.Error("fail to trigger pre-check agent task", "index", i, "err", err)
			return err
		}
		agentTaskIDs = append(agentTaskIDs, taskIDs...)

		stepInput := &RdmaToggleProgressEvaluateInput{
			CurrentStep:          "precheck",
			CurrentStepStartTime: workflow.Now(ctx),
			NodeIp:               agentInput.TargetIp,
		}
		if err := workflow.ExecuteActivity(
			ctxActionWait,
			h.RdmaToggleWaitingForAgentTasks,
			input,
			[]*agentpb.TaskInput{agentInput},
			taskIDs,
			stepInput,
			"precheck",
		).Get(ctx, nil); err != nil {
			logger.Error("fail to wait for pre-check agent task", "index", i, "err", err)
			return err
		}
	}
	realActionStarted = true

	// 4. Step 2: Configure all nodes
	logger.Info("Starting RDMA toggle config phase")
	var configAgentTaskInputs []*agentpb.TaskInput
	if err = workflow.ExecuteActivity(ctx, h.RdmaToggleGenerateConfigAgentInputs, input).Get(ctx, &configAgentTaskInputs); err != nil {
		logger.Error("fail to generate config agent inputs")
		return err
	}

	if configAgentTaskInputs == nil || len(configAgentTaskInputs) == 0 {
		err = errors.New("fail to generate config agent inputs")
		return err
	}

	var configTaskIDs []string
	if err = workflow.ExecuteActivity(ctx, h.RdmaToggleTriggerAgentTasks, input, configAgentTaskInputs).Get(ctx, &configTaskIDs); err != nil {
		logger.Error("fail to trigger config agent tasks", "err", err)
		return err
	}
	agentTaskIDs = append(agentTaskIDs, configTaskIDs...)

	// Update progress step to config
	rdmaToggleProgressEvaluateInput.CurrentStep = "config"
	rdmaToggleProgressEvaluateInput.CurrentStepStartTime = workflow.Now(ctx)

	if err = workflow.ExecuteActivity(ctxActionWait, h.RdmaToggleWaitingForAgentTasks, input, configAgentTaskInputs, configTaskIDs, rdmaToggleProgressEvaluateInput, "config").Get(ctx, nil); err != nil {
		logger.Error("fail to wait for config agent tasks", "err", err)
		return err
	}

	// 5. Step 3: Restart all nodes
	logger.Info("Starting RDMA toggle restart phase")
	var restartAgentTaskInputs []*agentpb.TaskInput
	if err = workflow.ExecuteActivity(ctx, h.RdmaToggleGenerateRestartAgentInputs, input).Get(ctx, &restartAgentTaskInputs); err != nil {
		logger.Error("fail to generate restart agent inputs")
		return err
	}

	if restartAgentTaskInputs == nil || len(restartAgentTaskInputs) == 0 {
		err = errors.New("fail to generate restart agent inputs")
		return err
	}

	var restartTaskIDs []string
	if err = workflow.ExecuteActivity(ctx, h.RdmaToggleTriggerAgentTasks, input, restartAgentTaskInputs).Get(ctx, &restartTaskIDs); err != nil {
		logger.Error("fail to trigger restart agent tasks", "err", err)
		return err
	}
	agentTaskIDs = append(agentTaskIDs, restartTaskIDs...)

	// Update progress step to restart
	rdmaToggleProgressEvaluateInput.CurrentStep = "restart"
	rdmaToggleProgressEvaluateInput.CurrentStepStartTime = workflow.Now(ctx)

	if err = workflow.ExecuteActivity(ctxActionWait, h.RdmaToggleWaitingForAgentTasks, input, restartAgentTaskInputs, restartTaskIDs, rdmaToggleProgressEvaluateInput, "restart").Get(ctx, nil); err != nil {
		logger.Error("fail to wait for restart agent tasks", "err", err)
		return err
	}

	// 6. update job state: success
	if err = workflow.ExecuteActivity(ctxStateUpdate, h.RdmaToggleUpdateJobStateWithSuccess, input).Get(ctx, nil); err != nil {
		logger.Error("failed update rdma toggle job state")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.RdmaToggleUpdateTowerTaskWithSuccess, input, towerTaskID).Get(ctx, nil); err != nil {
		logger.Error("fail to update rdma toggle tower task")
		return err
	}

	logger.Info("RdmaToggleWorkflow completed.")

	return nil
}
