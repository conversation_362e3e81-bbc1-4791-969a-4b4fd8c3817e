package config

// Temporal
const (
	TemporalNamespace     = "lcm-manager"
	TemporalWorkerName    = "lcm-manager-worker"
	WorkflowRetentionHour = 7 * 24
)

// LCM Manager

const (
	DBName       = "lcm_manager"
	UserIDHeader = "x-user-id"
	UserIPHeader = "x-user-ip"
)

// LCM Manager Agent constants
const (
	SmartxSSHUsername          = "smartx"
	SmartxSSHKeyPath           = "/home/<USER>/.ssh/smartx_id_rsa"
	AdminSSHUsername           = "admin"
	AdminSSHKeyPath            = "/home/<USER>/.ssh/admin_id_rsa"
	LcmHostPluginNamespace     = "lcm"
	LcmManagerAgentPackageName = "lcm-manager-agent"
)

// SMTXOS/SMTXZBS/SMTXELF API
var (
	TunaAPIAuthHeader = "X-SmartX-Token"
	TunaAPIToken      = "LazONJPtpMmTdVmEq1CmE9L6WP9GMSzh2BSxJFBrFMkhFglmSYzHyjGi1wMFR6D5" //nolint:gosec
)

const (
	LogDir              = "/var/log/lcm_manager"
	AvailabilityMapFile = "/etc/lcm-manager/availability-map.yaml"
	ActivityConfigFile  = "/etc/lcm-manager/activity.yaml"
)

type HostRole string

const (
	HostRoleMaster  HostRole = "master"
	HostRoleStorage HostRole = "storage"
)

const (
	SmtxOsSupportECVersion  = "6.1.0"
	SmtxZbsSupportECVersion = "5.6.0"
)

const (
	ActionConvertToMasterCheck  string = "CONVERT_TO_MASTER_CHECK"
	ActionConvertToStorageCheck string = "CONVERT_TO_STORAGE_CHECK"
	ActionConvertToMaster       string = "CONVERT_TO_MASTER"
	ActionConvertToStorage      string = "CONVERT_TO_STORAGE"
	ActionRemoveHostCheck       string = "REMOVE_HOST_CHECK"
	ActionRemoveHost            string = "REMOVE_HOST"
	ActionTimeSync              string = "TIME_SYNC"
	ActionRDMAToggle            string = "RDMA_TOGGLE"
)

// Cost Time evaluation params
const (
	PerNodeRoleConvertTimeMinute    = 3
	MetaRemoveNodePerNodeTimeMinute = 1
	MetaRemoveNodeBaseTimeMinute    = 3
	TimeSyncBaseTimeMinute          = 7
)

type HostLabel string

const (
	LcmManagerAction   string    = "LCM_MANAGER_ACTION"
	RoleConvertRunning HostLabel = "ROLE_CONVERT_RUNNING"
	RoleConvertFailed  HostLabel = "ROLE_CONVERT_FAILED"
	HostRemoveRunning  HostLabel = "HOST_REMOVE_RUNNING"
	HostRemoveFailed   HostLabel = "HOST_REMOVE_FAILED"

	LcmManagerHostRole   string    = "HOST_ROLE"
	HostLabelRoleMaster  HostLabel = "master"
	HostLabelRoleStorage HostLabel = "storage"
)

// Tower Task & Audit Event const
const TowerTaskResourceType = "LCMManagerJob"

type TowerTaskResourceMutation string

const (
	TowerTaskResourceMutationRoleConvertPrecheck TowerTaskResourceMutation = "RoleConvertPrecheck"
	TowerTaskResourceMutationRoleConvert         TowerTaskResourceMutation = "RoleConvert"
	TowerTaskResourceMutationHostRemovePrecheck  TowerTaskResourceMutation = "HostRemovePrecheck"
	TowerTaskResourceMutationHostRemove          TowerTaskResourceMutation = "HostRemove"
	TowerTaskResourceMutationTimeSync            TowerTaskResourceMutation = "TimeSync"
)

type TowerTaskType string

const (
	TaskTypeRoleConvertPrecheck TowerTaskType = "ROLE_CONVERT_PRECHECK"
	TaskTypeRoleConvert         TowerTaskType = "ROLE_CONVERT"
	TaskTypeHostRemovePrecheck  TowerTaskType = "HOST_REMOVE_PRECHECK"
	TaskTypeHostRemove          TowerTaskType = "HOST_REMOVE"
	TaskTypeTimeSync            TowerTaskType = "TIME_SYNC"
)

type TowerAuditEventType string

const (
	TowerAuditEventTypeRoleConvertPrecheck TowerAuditEventType = "ROLE_CONVERT_PRECHECK"
	TowerAuditEventTypeRoleConvert         TowerAuditEventType = "ROLE_CONVERT"
	TowerAuditEventTypeHostRemovePrecheck  TowerAuditEventType = "HOST_REMOVE_PRECHECK"
	TowerAuditEventTypeHostRemove          TowerAuditEventType = "HOST_REMOVE"
	TowerAuditEventTypeTimeSync            TowerAuditEventType = "TIME_SYNC"
)
