package config

type AvailabilityMap struct {
	Version        string          `yaml:"version" json:"version"`
	ProductActions []ProductAction `yaml:"product_actions" json:"product_actions"`
}

type ProductAction struct {
	ProductName string   `yaml:"product_name" json:"product_name"`
	Actions     []Action `yaml:"actions" json:"actions"`
}

type Action struct {
	Name             string            `yaml:"name" json:"name"`
	Type             string            `yaml:"type" json:"type"`
	VersionedActions []VersionedAction `yaml:"versioned_actions" json:"versioned_actions"`
}

type ActionFeature string

const (
	SupportMultiOfflineHosts ActionFeature = "SUPPORT_MULTI_OFFLINE_HOSTS"
)

type VersionedAction struct {
	Versions  []string        `yaml:"versions" json:"versions"`
	Features  []ActionFeature `yaml:"features,omitempty" json:"features,omitempty"`
	AgentCmds []AgentCmd      `yaml:"agent_cmds" json:"agent_cmds"`
}

type CmdName string

const (
	CmdNameConvertToStorage      CmdName = "convert_to_storage"
	CmdNameConvertToMaster       CmdName = "convert_to_master"
	CmdNameRestartService        CmdName = "restart_service"
	CmdNameStoragePoolRemoveNode CmdName = "storage_pool_remove_node"
	CmdNameMetaRemoveNode        CmdName = "meta_remove_node"
	CmdNameTimeSyncWithNtpServer CmdName = "time_sync_with_ntp_server"
	CmdNameTimeSyncInternal      CmdName = "time_sync_internal"
)

type AgentCmd struct {
	Name          CmdName `yaml:"name" json:"name"`
	Command       string  `yaml:"command" json:"command"`
	TimeoutFactor int     `yaml:"timeout_factor" json:"timeout_factor"`
	CmdQA         []CmdQA `yaml:"cmd_qa,omitempty" json:"cmd_qa,omitempty"`
}

type CmdQA struct {
	Query      string `yaml:"query" json:"query"`
	Answer     string `yaml:"answer" json:"answer"`
	PassedMark string `yaml:"passed_mark,omitempty" json:"passed_mark,omitempty"`
}

// temporal activity config
type ActivityOptions struct {
	StartToCloseTimeout     int     `yaml:"start_to_close_timeout"`
	RetryInitialInterval    int     `yaml:"retry_initial_interval"`
	RetryBackoffCoefficient float64 `yaml:"retry_backoff_coefficient"`
	RetryMaximumInterval    int     `yaml:"retry_maximum_interval"`
	RetryMaximumAttempts    int     `yaml:"retry_maximum_attempts"`
}

type ActivityConfigs map[ActivityOptionsName]*ActivityOptions

type ActivityOptionsName string

const (
	ActivityOptionsNameDefault                   ActivityOptionsName = "ActivityDefault"
	ActivityOptionsNamePrecheckDefault           ActivityOptionsName = "PrecheckActivityDefault"
	ActivityOptionsNameStateUpdate               ActivityOptionsName = "StateUpdateActivities"
	ActivityOptionsNameWaitingForRoleConvertDone ActivityOptionsName = "WaitingForRoleConvertDone"
	ActivityOptionsNameWaitingForRemoveHostDone  ActivityOptionsName = "WaitingForRemoveHostDone"
	ActivityOptionsNameWaitingForRemoveChunkDone ActivityOptionsName = "WaitingForRemoveChunkDone"
	ActivityOptionsNameWaitingForTimeSync        ActivityOptionsName = "WaitingForTimeSync"
	ActivityOptionsNameWaitingForRDMAToggle      ActivityOptionsName = "WaitingForRDMAToggle"
)
