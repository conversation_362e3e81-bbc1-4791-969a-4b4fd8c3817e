@node =  127.0.0.1
@port = 8080

@cluster = 04cd8cfb-cd70-4d31-82b7-b39f96118c0f
@host = a7b78b8e-5bc0-11f0-aab3-7d21660760d7
@host_ip = **************

@defaultToken = LazONJPtpMmTdVmEq1CmE9L6WP9GMSzh2BSxJFBrFMkhFglmSYzHyjGi1wMFR6D5

### 角色转换检查
# @name role_convert_check
POST http://{{node}}:{{port}}/api/v1/clusters/{{cluster}}/hosts/{{host}}/role_convert_check
content-type: application/json
x-user-id: root
x-user-ip: 127.0.0.1

{
    "client_id": "aaaa",
    "current_role": "ROLE_MASTER",
    "target_role": "ROLE_STORAGE"
}




### 角色转换检查结果
# @name role_convert_check_result
GET http://{{node}}:{{port}}/api/v1/clusters/{{cluster}}/hosts/{{host}}/role_convert_check_result?job_id={{role_convert_check.response.body.$.job_id}}


### 获取主机 host label
# @name host_labels
GET http://{{host_ip}}/api/v2/management/hosts/{{host}}/labels
content-type: application/json
X-SmartX-Token: {{defaultToken}}

### 角色转换
# @name role_convert
POST http://{{node}}:{{port}}/api/v1/clusters/{{cluster}}/hosts/{{host}}/role_convert
content-type: application/json
x-user-id: root
x-user-ip: 127.0.0.1

{
    "client_id": "aaaa",
    "current_role": "ROLE_MASTER",
    "target_role": "ROLE_STORAGE"
}

### 角色转换任务
# @name role_convert_task
GET http://{{node}}:{{port}}/api/v1/jobs/{{role_convert.response.body.$.jobId}}

### 时间同步任务
# @name sync_time_task
POST  http://{{node}}:{{port}}/api/v1/clusters/{{cluster}}/time/sync
content-type: application/json
x-user-id: cmcyldh4q04520958w0be4xmd
x-user-ip: 127.0.0.1

### 时间同步任务
# @name sync_time_task
POST http://*************:80/lcm-manager/api/v1/clusters/{{cluster}}/time/sync



### RDMA 开关任务
# @name rema_toggle
POST  http://{{node}}:{{port}}/api/v1/clusters/{{cluster}}/rdma/toggle
content-type: application/json
x-user-id: cmcyldh4q04520958w0be4xmd
x-user-ip: 127.0.0.1
